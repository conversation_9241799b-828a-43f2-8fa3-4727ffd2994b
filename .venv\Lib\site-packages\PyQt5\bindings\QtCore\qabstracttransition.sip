// qabstracttransition.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractTransition : public QObject
{
%TypeHeaderCode
#include <qabstracttransition.h>
%End

public:
    QAbstractTransition(QState *sourceState /TransferThis/ = 0);
    virtual ~QAbstractTransition();
    QState *sourceState() const;
    QAbstractState *targetState() const;
    void setTargetState(QAbstractState *target /KeepReference=0/);
    QList<QAbstractState *> targetStates() const;
    void setTargetStates(const QList<QAbstractState *> &targets /KeepReference=0/);
    QStateMachine *machine() const;
    void addAnimation(QAbstractAnimation *animation /GetWrapper/);
%MethodCode
        // We want to keep a reference to the animation but this is in addition to the
        // existing ones and does not replace them - so we can't use /KeepReference/.
        sipCpp->addAnimation(a0);
        
        // Use the user object as a list of the references.
        PyObject *user = sipGetUserObject((sipSimpleWrapper *)sipSelf);
        
        if (!user)
        {
            user = PyList_New(0);
            sipSetUserObject((sipSimpleWrapper *)sipSelf, user);
        }
        
        if (user)
            PyList_Append(user, a0Wrapper);
%End

    void removeAnimation(QAbstractAnimation *animation /GetWrapper/);
%MethodCode
        // Discard the extra animation reference that we took in addAnimation().
        sipCpp->removeAnimation(a0);
        
        // Use the user object as a list of the references.
        PyObject *user = sipGetUserObject((sipSimpleWrapper *)sipSelf);
        
        if (user)
        {
            Py_ssize_t i = 0;
            
            // Note that we deal with an object appearing in the list more than once.
            while (i < PyList_Size(user))
                if (PyList_GetItem(user, i) == a0Wrapper)
                    PyList_SetSlice(user, i, i + 1, NULL);
                else
                    ++i;
        }
%End

    QList<QAbstractAnimation *> animations() const;

signals:
    void triggered();
%If (Qt_5_4_0 -)
    void targetStateChanged();
%End
%If (Qt_5_4_0 -)
    void targetStatesChanged();
%End

protected:
    virtual bool eventTest(QEvent *event) = 0;
    virtual void onTransition(QEvent *event) = 0;
    virtual bool event(QEvent *e);

public:
%If (Qt_5_5_0 -)

    enum TransitionType
    {
        ExternalTransition,
        InternalTransition,
    };

%End
%If (Qt_5_5_0 -)
    QAbstractTransition::TransitionType transitionType() const;
%End
%If (Qt_5_5_0 -)
    void setTransitionType(QAbstractTransition::TransitionType type);
%End
};
