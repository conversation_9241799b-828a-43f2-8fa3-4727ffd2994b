#!/usr/bin/env python3
"""
Diagnostic tool for Blackhat Control System
"""
import sys
import os
import importlib
import platform

def check_python_version():
    """Check Python version"""
    print(f"Python version: {platform.python_version()}")
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        return False
    print("✓ Python version OK")
    return True

def check_dependencies():
    """Check required dependencies"""
    required_packages = [
        "PyQt5", "requests", "cryptography", "flask", 
        "flask_httpauth", "waitress", "gunicorn"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✓ {package} installed")
        except ImportError:
            print(f"❌ {package} not installed")
            missing_packages.append(package)
    
    if missing_packages:
        print("\nMissing packages. Install with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_files():
    """Check if required files exist"""
    required_files = [
        "blackhat_control_gui.py",
        "sms_panel_connector.py",
        "config.py",
        "start.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ {file} not found")
            missing_files.append(file)
        else:
            print(f"✓ {file} found")
    
    if missing_files:
        print("\nMissing files. Make sure all files are in the current directory.")
        return False
    
    return True

def check_imports():
    """Check if imports in main files work"""
    print("\nChecking imports in blackhat_control_gui.py...")
    try:
        with open("blackhat_control_gui.py", "r") as f:
            content = f.read()
        
        # Execute just the imports
        import_lines = []
        for line in content.split("\n"):
            if line.startswith("import ") or line.startswith("from "):
                import_lines.append(line)
        
        import_code = "\n".join(import_lines)
        try:
            exec(import_code)
            print("✓ All imports in blackhat_control_gui.py are valid")
            return True
        except Exception as e:
            print(f"❌ Import error in blackhat_control_gui.py: {str(e)}")
            return False
    except Exception as e:
        print(f"❌ Error reading blackhat_control_gui.py: {str(e)}")
        return False

def main():
    """Main diagnostic function"""
    print("🔵 BLACKHAT CONTROL SYSTEM - DIAGNOSTICS")
    print("=" * 50)
    
    python_ok = check_python_version()
    deps_ok = check_dependencies()
    files_ok = check_files()
    imports_ok = check_imports()
    
    print("\n" + "=" * 50)
    if python_ok and deps_ok and files_ok and imports_ok:
        print("✅ All diagnostics passed. The system should be able to start.")
        print("\nTry running the GUI directly with:")
        print("python blackhat_control_gui.py")
    else:
        print("❌ Some diagnostics failed. Please fix the issues above.")
    
    print("\nAdditional troubleshooting:")
    print("1. Make sure you're in the correct directory")
    print("2. Try installing all dependencies: pip install PyQt5 requests cryptography flask flask-httpauth waitress gunicorn")
    print("3. Check for any error messages when running the GUI directly")

if __name__ == "__main__":
    main()
