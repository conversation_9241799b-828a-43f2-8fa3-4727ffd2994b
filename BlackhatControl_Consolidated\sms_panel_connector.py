"""
SMS Panel Connection Module
"""
import requests
import json
import time
import base64
import hashlib
import hmac
from datetime import datetime

class SmsPanelConnector:
    def __init__(self, panel_type="smpp", config=None):
        """
        Initialize SMS Panel connector
        
        panel_type options:
        - "smpp": SMPP protocol connection (most SMS gateways)
        - "api": REST API connection (BulkSMS, Twilio, etc.)
        - "gateway": SMS Gateway connection
        - "gsm": Direct GSM modem connection
        """
        self.panel_type = panel_type
        self.config = config or {}
        self.session = requests.Session()
        self.authenticated = False
        self.balance = 0
        self.rate_per_sms = 0.01  # Default cost per SMS
        
    def connect(self):
        """Connect to SMS panel"""
        if self.panel_type == "smpp":
            return self._connect_smpp()
        elif self.panel_type == "api":
            return self._connect_api()
        elif self.panel_type == "gateway":
            return self._connect_gateway()
        elif self.panel_type == "gsm":
            return self._connect_gsm()
        else:
            return False, "Unknown panel type"
    
    def _connect_smpp(self):
        """Connect to SMPP server"""
        try:
            # SMPP connection would use smpplib in a real implementation
            # This is a simplified version
            host = self.config.get("host", "")
            port = self.config.get("port", 2775)
            username = self.config.get("username", "")
            password = self.config.get("password", "")
            
            if not host or not username or not password:
                return False, "Missing SMPP configuration"
            
            # Simulate SMPP connection
            time.sleep(1)
            self.authenticated = True
            self._check_balance()
            
            return True, "Connected to SMPP server"
        except Exception as e:
            return False, f"SMPP connection error: {str(e)}"
    
    def _connect_api(self):
        """Connect to REST API"""
        try:
            api_url = self.config.get("api_url", "")
            api_key = self.config.get("api_key", "")
            api_secret = self.config.get("api_secret", "")
            
            if not api_url or not api_key:
                return False, "Missing API configuration"
            
            # Add authentication headers
            self.session.headers.update({
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            })
            
            # Test connection with balance check
            response = self.session.get(f"{api_url}/balance")
            
            if response.status_code == 200:
                data = response.json()
                self.authenticated = True
                self.balance = data.get("balance", 0)
                self.rate_per_sms = data.get("rate", 0.01)
                return True, "Connected to API server"
            else:
                return False, f"API connection error: {response.status_code}"
        except Exception as e:
            return False, f"API connection error: {str(e)}"
    
    def _connect_gateway(self):
        """Connect to SMS Gateway"""
        try:
            gateway_url = self.config.get("gateway_url", "")
            username = self.config.get("username", "")
            password = self.config.get("password", "")
            
            if not gateway_url or not username or not password:
                return False, "Missing Gateway configuration"
            
            # Create authentication token
            auth_string = base64.b64encode(f"{username}:{password}".encode()).decode()
            self.session.headers.update({
                "Authorization": f"Basic {auth_string}",
                "Content-Type": "application/json"
            })
            
            # Test connection
            response = self.session.get(f"{gateway_url}/status")
            
            if response.status_code == 200:
                self.authenticated = True
                self._check_balance()
                return True, "Connected to SMS Gateway"
            else:
                return False, f"Gateway connection error: {response.status_code}"
        except Exception as e:
            return False, f"Gateway connection error: {str(e)}"
    
    def _connect_gsm(self):
        """Connect to GSM modem"""
        # This would use a library like pyserial in a real implementation
        return False, "GSM modem connection not implemented"
    
    def _check_balance(self):
        """Check account balance"""
        if not self.authenticated:
            return False, "Not authenticated"
        
        try:
            if self.panel_type == "smpp":
                # Simulate balance check
                self.balance = 500
                self.rate_per_sms = 0.01
            elif self.panel_type == "api":
                # Already checked in connect
                pass
            elif self.panel_type == "gateway":
                response = self.session.get(f"{self.config.get('gateway_url')}/balance")
                if response.status_code == 200:
                    data = response.json()
                    self.balance = data.get("balance", 0)
                    self.rate_per_sms = data.get("rate", 0.01)
            
            return True, {"balance": self.balance, "rate": self.rate_per_sms}
        except Exception as e:
            return False, f"Balance check error: {str(e)}"
    
    def send_sms(self, phone, message, sender=None):
        """Send single SMS"""
        if not self.authenticated:
            return False, "Not authenticated"
        
        try:
            if self.panel_type == "smpp":
                return self._send_smpp(phone, message, sender)
            elif self.panel_type == "api":
                return self._send_api(phone, message, sender)
            elif self.panel_type == "gateway":
                return self._send_gateway(phone, message, sender)
            elif self.panel_type == "gsm":
                return self._send_gsm(phone, message, sender)
            else:
                return False, "Unknown panel type"
        except Exception as e:
            return False, f"Send SMS error: {str(e)}"
    
    def send_bulk_sms(self, phones, message, sender=None):
        """Send bulk SMS"""
        if not self.authenticated:
            return False, "Not authenticated"
        
        try:
            if self.panel_type == "api" and self.config.get("supports_bulk", False):
                return self._send_bulk_api(phones, message, sender)
            else:
                # Fall back to individual sending
                results = []
                success_count = 0
                
                for phone in phones:
                    success, response = self.send_sms(phone, message, sender)
                    results.append({
                        "phone": phone,
                        "success": success,
                        "response": response
                    })
                    
                    if success:
                        success_count += 1
                
                return True, {
                    "total": len(phones),
                    "success": success_count,
                    "failed": len(phones) - success_count,
                    "details": results
                }
        except Exception as e:
            return False, f"Send bulk SMS error: {str(e)}"
    
    def _send_smpp(self, phone, message, sender=None):
        """Send SMS via SMPP"""
        # Simulate SMPP sending
        time.sleep(0.5)
        message_id = hashlib.md5(f"{phone}{message}{time.time()}".encode()).hexdigest()
        return True, {"message_id": message_id, "status": "SENT"}
    
    def _send_api(self, phone, message, sender=None):
        """Send SMS via API"""
        api_url = self.config.get("api_url", "")
        
        payload = {
            "to": phone,
            "message": message,
            "from": sender or self.config.get("default_sender", "INFO")
        }
        
        response = self.session.post(f"{api_url}/send", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            return True, data
        else:
            return False, f"API send error: {response.status_code}"
    
    def _send_bulk_api(self, phones, message, sender=None):
        """Send bulk SMS via API"""
        api_url = self.config.get("api_url", "")
        
        payload = {
            "to": phones,
            "message": message,
            "from": sender or self.config.get("default_sender", "INFO")
        }
        
        response = self.session.post(f"{api_url}/send-bulk", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            return True, data
        else:
            return False, f"API bulk send error: {response.status_code}"
    
    def _send_gateway(self, phone, message, sender=None):
        """Send SMS via Gateway"""
        gateway_url = self.config.get("gateway_url", "")
        
        payload = {
            "to": phone,
            "text": message,
            "from": sender or self.config.get("default_sender", "INFO")
        }
        
        response = self.session.post(f"{gateway_url}/send", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            return True, data
        else:
            return False, f"Gateway send error: {response.status_code}"
    
    def _send_gsm(self, phone, message, sender=None):
        """Send SMS via GSM modem"""
        # This would use a library like pyserial in a real implementation
        return False, "GSM modem sending not implemented"