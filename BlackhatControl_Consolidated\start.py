#!/usr/bin/env python3
"""
BLACKHAT CONTROL SYSTEM
Professional Stealth Interface
"""

import subprocess
import sys
import os

def check_requirements():
    """Check if required packages are installed"""
    try:
        import requests
        print("✓ Requirements satisfied")
        return True
    except ImportError:
        print("❌ Missing requirements")
        print("Installing required packages...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
            print("✓ Requirements installed")
            return True
        except:
            print("❌ Failed to install requirements")
            print("Please run: pip install requests")
            return False

def main():
    print("🔵 BLACKHAT CONTROL SYSTEM")
    print("⚡ Professional Stealth Interface")
    print("=" * 40)
    
    if not check_requirements():
        return
    
    print("\n🚀 Starting control system...")
    
    try:
        # Start the main server
        subprocess.run([sys.executable, "http_server.py"])
    except KeyboardInterrupt:
        print("\n🔵 System shutdown")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
