#!/usr/bin/env python3
"""
Simplified launcher for Blackhat Control GUI
"""
import sys
import os
import traceback

def main():
    """Main function to start the GUI"""
    print("🔵 BLACKHAT CONTROL SYSTEM - GUI LAUNCHER")
    print("=" * 50)
    
    try:
        # Add current directory to path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        print("Importing BlackhatControlGUI class...")
        from blackhat_control_gui import BlackhatControlGUI
        
        print("Importing QApplication...")
        from PyQt5.QtWidgets import QApplication
        
        print("Creating QApplication...")
        app = QApplication(sys.argv)
        
        print("Creating BlackhatControlGUI instance...")
        window = BlackhatControlGUI()
        
        print("Starting application event loop...")
        sys.exit(app.exec_())
    
    except Exception as e:
        print(f"❌ Error starting GUI: {str(e)}")
        print("\nDetailed error information:")
        traceback.print_exc()
        
        print("\nTroubleshooting suggestions:")
        print("1. Make sure PyQt5 is installed: pip install PyQt5")
        print("2. Check that all required modules are in the same directory")
        print("3. Look for import errors in the traceback above")
        
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()