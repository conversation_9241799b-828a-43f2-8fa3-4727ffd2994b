@echo off 
echo 🔵 BLACKHAT CONTROL SYSTEM 
echo ⚡ Professional Stealth Interface 
echo ======================================= 
 
REM Check if Python is installed 
python --version >nul 2>&1 
if %errorlevel% neq 0 ( 
    echo ❌ Python not found. Please install Python 3.7 or higher. 
    echo    Download from: https://www.python.org/downloads/ 
    pause 
    exit /b 1 
) 
 
REM Check Python version 
echo ✓ Python %PYVER% detected 
 
REM Create necessary directories if they don't exist 
echo Checking directories... 
if not exist logs mkdir logs && echo ✓ Created logs directory 
if not exist data mkdir data && echo ✓ Created data directory 
if not exist configs mkdir configs && echo ✓ Created configs directory 
if not exist apk mkdir apk && echo ✓ Created apk directory 
if not exist apk_configs mkdir apk_configs && echo ✓ Created apk_configs directory 
if not exist reports mkdir reports && echo ✓ Created reports directory 
 
REM Install required packages 
echo Installing required packages... 
pip install requests cryptography flask flask-httpauth PyQt5 waitress gunicorn 
 
REM Check for Tor proxy 
echo Checking Tor proxy... 
curl --socks5 127.0.0.1:9050 https://check.torproject.org/ >nul 2>&1 
if %errorlevel% equ 0 ( 
    echo ✓ Tor proxy detected 
) else ( 
    echo ⚠️ Tor proxy not detected. Some features may be limited. 
) 
 
REM Check for VPN proxy 
echo Checking VPN proxy... 
curl --proxy 127.0.0.1:8080 https://api.ipify.org/ >nul 2>&1 
if %errorlevel% equ 0 ( 
    echo ✓ VPN proxy detected 
) else ( 
    echo ⚠️ VPN proxy not detected. Some features may be limited. 
) 
 
REM Start the SMS Panel Connector 
echo Starting SMS Panel Connector... 
start "SMS Panel" cmd /c "python sms_panel_connector.py" 
echo ✓ SMS Panel started on http://localhost:5000 
 
REM Start the main GUI application 
echo Starting Blackhat Control GUI... 
start "Blackhat Control" cmd /c "python blackhat_control_gui.py" 
echo ✓ GUI application started 
 
echo. 
echo 🚀 BLACKHAT CONTROL SYSTEM STARTED SUCCESSFULLY 
echo ======================================= 
echo ✓ SMS Panel: http://localhost:5000 
echo ✓ Main GUI: Running in separate window 
echo. 
echo Press any key to exit this console (system will continue running)... 
pause >nul 
