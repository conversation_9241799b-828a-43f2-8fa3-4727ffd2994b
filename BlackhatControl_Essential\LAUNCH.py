#!/usr/bin/env python3
"""
BLACKHAT CONTROL SYSTEM - MAIN LAUNCHER
Essential Files Only - Complete System
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import subprocess

class SystemLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔵 BLACKHAT CONTROL SYSTEM")
        self.root.geometry("600x500")
        self.root.configure(bg='#000000')
        self.root.resizable(False, False)
        
        # Center window
        self.center_window()
        
        # Create UI
        self.create_ui()
        
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"600x500+{x}+{y}")
        
    def create_ui(self):
        """Create the main launcher UI"""
        # Title
        title_frame = tk.Frame(self.root, bg='#000000')
        title_frame.pack(fill='x', pady=30)
        
        title = tk.Label(
            title_frame,
            text="⚠️ BLACKHAT CONTROL SYSTEM ⚠️",
            font=("Arial", 22, "bold"),
            fg="#4a9eff",
            bg="#000000"
        )
        title.pack()
        
        subtitle = tk.Label(
            title_frame,
            text="Professional SMS & Mobile Exploitation Framework",
            font=("Arial", 12),
            fg="#7bb3ff",
            bg="#000000"
        )
        subtitle.pack(pady=10)
        
        version = tk.Label(
            title_frame,
            text="Essential Edition • No License Required",
            font=("Arial", 11),
            fg="#00ff88",
            bg="#000000"
        )
        version.pack(pady=5)
        
        # Interface selection
        interface_frame = tk.Frame(self.root, bg="#001122", relief="solid", bd=2)
        interface_frame.pack(fill='x', padx=40, pady=30)
        
        tk.Label(
            interface_frame,
            text="🚀 CHOOSE YOUR INTERFACE:",
            font=("Arial", 14, "bold"),
            fg="#4a9eff",
            bg="#001122"
        ).pack(pady=15)
        
        # PyQt5 GUI Button
        gui_btn = tk.Button(
            interface_frame,
            text="🖥️ PROFESSIONAL GUI",
            command=self.launch_gui,
            font=("Arial", 13, "bold"),
            bg="#4a9eff",
            fg="#000000",
            relief="flat",
            padx=30,
            pady=12
        )
        gui_btn.pack(pady=8)
        
        # Web Interface Button
        web_btn = tk.Button(
            interface_frame,
            text="🌐 WEB INTERFACE",
            command=self.launch_web,
            font=("Arial", 13, "bold"),
            bg="#00ff88",
            fg="#000000",
            relief="flat",
            padx=30,
            pady=12
        )
        web_btn.pack(pady=8)
        
        # Mobile Testing Button
        mobile_btn = tk.Button(
            interface_frame,
            text="📱 MOBILE TESTING",
            command=self.launch_mobile,
            font=("Arial", 13, "bold"),
            bg="#ff6600",
            fg="#000000",
            relief="flat",
            padx=30,
            pady=12
        )
        mobile_btn.pack(pady=8)
        
        # System info
        info_frame = tk.Frame(self.root, bg="#001122", relief="solid", bd=2)
        info_frame.pack(fill='both', expand=True, padx=40, pady=20)
        
        tk.Label(
            info_frame,
            text="🎯 SYSTEM CAPABILITIES:",
            font=("Arial", 12, "bold"),
            fg="#4a9eff",
            bg="#001122"
        ).pack(pady=10)
        
        capabilities = [
            "📱 Anonymous SMS sending (Multiple providers)",
            "🏴‍☠️ APK generation for Android targeting", 
            "👁️ Remote surveillance simulation",
            "💰 Banking & crypto data extraction",
            "🔄 Dynamic port rotation for stealth",
            "🛡️ Tor/VPN integration for anonymity",
            "🌍 Worldwide targeting capabilities"
        ]
        
        for capability in capabilities:
            tk.Label(
                info_frame,
                text=capability,
                font=("Arial", 9),
                fg="#7bb3ff",
                bg="#001122",
                anchor='w'
            ).pack(fill='x', padx=15, pady=1)
        
        # Status
        self.status_label = tk.Label(
            self.root,
            text="Ready to launch • All systems operational",
            font=("Arial", 10, "bold"),
            fg="#00ff88",
            bg="#000000"
        )
        self.status_label.pack(pady=10)
        
    def launch_gui(self):
        """Launch PyQt5 GUI interface"""
        try:
            self.status_label.config(text="🚀 Launching Professional GUI...", fg="#ffaa00")
            self.root.update()
            
            subprocess.Popen([sys.executable, 'gui.py'])
            
            self.status_label.config(text="✅ Professional GUI launched", fg="#00ff88")
            
        except Exception as e:
            self.status_label.config(text=f"❌ Error: {e}", fg="#ff6666")
            messagebox.showerror("Error", f"Failed to launch GUI: {e}")
    
    def launch_web(self):
        """Launch web interface"""
        try:
            self.status_label.config(text="🌐 Starting web interface...", fg="#ffaa00")
            self.root.update()
            
            subprocess.Popen([sys.executable, 'web.py'])
            
            self.status_label.config(text="✅ Web interface launched", fg="#00ff88")
            
        except Exception as e:
            self.status_label.config(text=f"❌ Error: {e}", fg="#ff6666")
            messagebox.showerror("Error", f"Failed to launch web interface: {e}")
    
    def launch_mobile(self):
        """Launch mobile testing server"""
        try:
            self.status_label.config(text="📱 Starting mobile server...", fg="#ffaa00")
            self.root.update()
            
            subprocess.Popen([sys.executable, 'mobile.py'])
            
            self.status_label.config(text="✅ Mobile server launched", fg="#00ff88")
            
        except Exception as e:
            self.status_label.config(text=f"❌ Error: {e}", fg="#ff6666")
            messagebox.showerror("Error", f"Failed to launch mobile server: {e}")
    
    def run(self):
        """Run the launcher"""
        self.root.mainloop()

def main():
    """Main entry point"""
    print("🔵 BLACKHAT CONTROL SYSTEM - ESSENTIAL EDITION")
    print("=" * 50)
    print("Professional SMS & Mobile Exploitation Framework")
    print("Essential Files Only - No License Required")
    print("=" * 50)
    
    try:
        launcher = SystemLauncher()
        launcher.run()
    except Exception as e:
        print(f"❌ Error starting launcher: {e}")
        messagebox.showerror("Error", f"Failed to start system launcher: {e}")

if __name__ == "__main__":
    main()
