#!/usr/bin/env python3
"""
Essential Mobile Testing Server
Mobile-optimized popup testing interface
"""

import http.server
import socketserver
import webbrowser
from threading import Thread
import time
import random

class MobileHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        """Serve mobile testing interface"""
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>🔵 Mobile Security Test</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #0a1929, #001122);
            color: #fff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .title {
            color: #4a9eff;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #7bb3ff;
            font-size: 14px;
        }
        
        .test-panel {
            background: rgba(0, 17, 34, 0.9);
            border: 2px solid #4a9eff;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .panel-title {
            color: #4a9eff;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .btn {
            width: 100%;
            background: #4a9eff;
            color: #000;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #6bb3ff;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #ff4444;
            color: #fff;
        }
        
        .btn-warning {
            background: #ff6600;
        }
        
        .btn-success {
            background: #00ff88;
        }
        
        .popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            z-index: 1000;
        }
        
        .popup-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            max-width: 350px;
            width: 90%;
            color: #000;
        }
        
        .popup-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .popup-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .popup-text {
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .popup-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 5px;
        }
        
        .popup-btn.cancel {
            background: #f44336;
        }
        
        .log {
            background: #000;
            border: 1px solid #4a9eff;
            border-radius: 10px;
            padding: 15px;
            height: 150px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            color: #00ff88;
        }
        
        .status {
            text-align: center;
            padding: 10px;
            background: rgba(0, 255, 136, 0.1);
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📱 Mobile Security Test</div>
            <div class="subtitle">Educational Popup Testing</div>
        </div>
        
        <div class="status">
            <strong>🔒 Mobile Testing Server Active</strong>
        </div>
        
        <div class="test-panel">
            <div class="panel-title">🎯 Popup Tests</div>
            
            <button class="btn" onclick="showPlayStoreUpdate()">
                📱 Play Store Update
            </button>
            
            <button class="btn btn-warning" onclick="showSecurityAlert()">
                ⚠️ Security Alert
            </button>
            
            <button class="btn btn-danger" onclick="showVirusWarning()">
                🦠 Virus Warning
            </button>
            
            <button class="btn btn-success" onclick="showBankingUpdate()">
                🏦 Banking Update
            </button>
        </div>
        
        <div class="test-panel">
            <div class="panel-title">📋 Test Log</div>
            <div id="testLog" class="log">
                [Ready] Mobile testing server initialized<br>
                [Ready] Popup tests available<br>
                [Ready] Tap buttons above to test popups<br>
            </div>
        </div>
        
        <div class="test-panel">
            <div class="panel-title">🔧 Actions</div>
            <button class="btn" onclick="clearLog()">🗑️ Clear Log</button>
            <button class="btn" onclick="testAll()">🚀 Test All Popups</button>
        </div>
    </div>
    
    <!-- Popup Overlay -->
    <div id="popup" class="popup">
        <div class="popup-content">
            <div id="popupIcon" class="popup-icon">📱</div>
            <div id="popupTitle" class="popup-title">Update Required</div>
            <div id="popupText" class="popup-text">Your device needs a security update.</div>
            <button class="popup-btn" onclick="handlePopupAction('install')">Install Now</button>
            <button class="popup-btn cancel" onclick="closePopup()">Cancel</button>
        </div>
    </div>
    
    <script>
        function getTimestamp() {
            return new Date().toLocaleTimeString();
        }
        
        function logMessage(message, color = '#00ff88') {
            const log = document.getElementById('testLog');
            log.innerHTML += '<div style="color: ' + color + ';">[' + getTimestamp() + '] ' + message + '</div>';
            log.scrollTop = log.scrollHeight;
        }
        
        function showPopup(icon, title, text) {
            document.getElementById('popupIcon').textContent = icon;
            document.getElementById('popupTitle').textContent = title;
            document.getElementById('popupText').textContent = text;
            document.getElementById('popup').style.display = 'block';
            
            // Vibrate if supported
            if (navigator.vibrate) {
                navigator.vibrate([200, 100, 200]);
            }
        }
        
        function closePopup() {
            document.getElementById('popup').style.display = 'none';
            logMessage('❌ User cancelled popup', '#ff6666');
        }
        
        function handlePopupAction(action) {
            const title = document.getElementById('popupTitle').textContent;
            closePopup();
            
            if (action === 'install') {
                logMessage('✅ User clicked "Install" on: ' + title, '#00ff88');
                logMessage('🎯 Simulating APK download...', '#ffaa00');
                setTimeout(() => {
                    logMessage('📱 APK deployment successful (simulated)', '#00ff88');
                }, 2000);
            }
        }
        
        function showPlayStoreUpdate() {
            logMessage('📱 Showing Play Store update popup', '#4a9eff');
            showPopup(
                '📱',
                'Google Play Store Update',
                'A new version of Google Play Store is available. Update now to get the latest features and security improvements.'
            );
        }
        
        function showSecurityAlert() {
            logMessage('⚠️ Showing security alert popup', '#ff6600');
            showPopup(
                '⚠️',
                'Security Alert',
                'Your device may be at risk. Install the latest security patch to protect your personal information.'
            );
        }
        
        function showVirusWarning() {
            logMessage('🦠 Showing virus warning popup', '#ff4444');
            showPopup(
                '🦠',
                'Virus Detected!',
                'Malicious software detected on your device. Install antivirus protection immediately to secure your data.'
            );
        }
        
        function showBankingUpdate() {
            logMessage('🏦 Showing banking update popup', '#00ff88');
            showPopup(
                '🏦',
                'Banking App Update',
                'Your banking app requires an urgent security update. Install now to continue using mobile banking services.'
            );
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '[Ready] Log cleared<br>';
        }
        
        function testAll() {
            logMessage('🚀 Starting automated popup test sequence...', '#4a9eff');
            
            const tests = [
                { func: showPlayStoreUpdate, delay: 1000 },
                { func: showSecurityAlert, delay: 3000 },
                { func: showVirusWarning, delay: 5000 },
                { func: showBankingUpdate, delay: 7000 }
            ];
            
            tests.forEach((test, index) => {
                setTimeout(() => {
                    test.func();
                    setTimeout(closePopup, 1500); // Auto-close after 1.5 seconds
                }, test.delay);
            });
            
            setTimeout(() => {
                logMessage('✅ Automated test sequence completed', '#00ff88');
            }, 9000);
        }
        
        // Auto-close popup after 10 seconds
        let autoCloseTimer;
        
        function showPopup(icon, title, text) {
            document.getElementById('popupIcon').textContent = icon;
            document.getElementById('popupTitle').textContent = title;
            document.getElementById('popupText').textContent = text;
            document.getElementById('popup').style.display = 'block';
            
            // Clear existing timer
            if (autoCloseTimer) {
                clearTimeout(autoCloseTimer);
            }
            
            // Set auto-close timer
            autoCloseTimer = setTimeout(() => {
                closePopup();
                logMessage('⏰ Popup auto-closed after timeout', '#ffaa00');
            }, 10000);
            
            // Vibrate if supported
            if (navigator.vibrate) {
                navigator.vibrate([200, 100, 200]);
            }
        }
        
        // Initialize
        logMessage('📱 Mobile testing interface loaded', '#4a9eff');
        logMessage('🎯 Ready for popup testing', '#00ff88');
    </script>
</body>
</html>
            """
            
            self.wfile.write(html_content.encode())
        else:
            super().do_GET()

def start_mobile_server(port=8081):
    """Start the mobile testing server"""
    try:
        with socketserver.TCPServer(("", port), MobileHandler) as httpd:
            print(f"📱 Mobile testing server starting on http://localhost:{port}")
            print("🔵 Blackhat Control System - Mobile Testing")
            print("=" * 50)
            
            # Open browser
            def open_browser():
                time.sleep(1)
                webbrowser.open(f'http://localhost:{port}')
            
            Thread(target=open_browser, daemon=True).start()
            
            print(f"✅ Server running on port {port}")
            print("📱 Opening mobile interface...")
            print("🎯 Test fake popups on your phone by visiting the URL")
            print("Press Ctrl+C to stop")
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
    except Exception as e:
        print(f"❌ Server error: {e}")

def main():
    """Main entry point"""
    start_mobile_server()

if __name__ == "__main__":
    main()
