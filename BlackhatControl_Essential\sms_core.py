#!/usr/bin/env python3
"""
SMS Core - Essential SMS Functionality
Simplified SMS manager for the essential edition
"""

import requests
import random
import time
from datetime import datetime
import json

class SmsCore:
    def __init__(self):
        self.providers = {
            'textbelt': {
                'url': 'https://textbelt.com/text',
                'enabled': True,
                'quota': 1
            },
            'simulation': {
                'enabled': True,
                'quota': 999
            }
        }
        
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
        
        self.sent_messages = []
        
    def format_phone_number(self, phone):
        """Format phone number for SMS sending"""
        phone = ''.join(filter(str.isdigit, phone))
        
        if len(phone) == 10:
            phone = '1' + phone
        
        return phone
        
    def send_via_textbelt(self, phone, message):
        """Send SMS via TextBelt (free service)"""
        try:
            formatted_phone = self.format_phone_number(phone)
            
            data = {
                'phone': formatted_phone,
                'message': message,
                'key': 'textbelt'
            }
            
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            response = requests.post(
                self.providers['textbelt']['url'],
                data=data,
                headers=headers,
                timeout=30
            )
            
            result = response.json()
            
            if result.get('success'):
                return {
                    'success': True,
                    'provider': 'textbelt',
                    'message_id': result.get('textId'),
                    'quota_remaining': result.get('quotaRemaining', 0)
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Unknown error'),
                    'provider': 'textbelt'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'provider': 'textbelt'
            }
    
    def send_simulation(self, phone, message):
        """Simulate SMS sending for demo purposes"""
        try:
            # Simulate network delay
            time.sleep(random.uniform(0.5, 2.0))
            
            # Random success rate (90% success)
            success = random.random() > 0.1
            
            if success:
                return {
                    'success': True,
                    'provider': 'simulation',
                    'message_id': f'sim_{random.randint(100000, 999999)}',
                    'simulated': True
                }
            else:
                errors = ['Network timeout', 'Invalid number', 'Rate limited', 'Service unavailable']
                return {
                    'success': False,
                    'error': random.choice(errors),
                    'provider': 'simulation',
                    'simulated': True
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'provider': 'simulation',
                'simulated': True
            }
    
    def send_sms(self, phone, message, provider='auto'):
        """Send SMS with specified provider"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Choose provider
        if provider == 'auto':
            # Try TextBelt first, fallback to simulation
            provider = 'textbelt'
        
        result = None
        
        # Send via selected provider
        if provider == 'textbelt':
            result = self.send_via_textbelt(phone, message)
            # If TextBelt fails, fallback to simulation
            if not result.get('success'):
                result = self.send_simulation(phone, message)
                result['fallback'] = True
        else:
            result = self.send_simulation(phone, message)
        
        # Log the message
        log_entry = {
            'timestamp': timestamp,
            'phone': phone,
            'message': message,
            'provider': provider,
            'result': result
        }
        
        self.sent_messages.append(log_entry)
        
        return result
    
    def send_bulk_sms(self, phone_list, message, delay=2):
        """Send bulk SMS to multiple numbers"""
        results = []
        
        for i, phone in enumerate(phone_list):
            if i > 0:
                time.sleep(delay)
            
            # Personalize message for each number
            personal_message = message.replace('{code}', str(random.randint(100000, 999999)))
            personal_message = personal_message.replace('{link}', 'http://secure-update.app/android')
            personal_message = personal_message.replace('{app}', 'System Security')
            
            result = self.send_sms(phone, personal_message)
            result['bulk_index'] = i + 1
            result['total_count'] = len(phone_list)
            results.append(result)
        
        return results
    
    def deploy_fake_update(self, phone):
        """Deploy fake app update via SMS"""
        update_messages = [
            "🔒 System Security update required. Install now: http://secure-update.app/android",
            "⚠️ Critical security patch available. Download: http://system-update.net/security", 
            "🛡️ Android vulnerability fix ready. Update: http://android-security.org/patch"
        ]
        
        message = random.choice(update_messages)
        
        result = self.send_sms(phone, message)
        result['deployment_type'] = 'fake_update'
        
        return result
    
    def get_message_history(self, limit=50):
        """Get recent message history"""
        return self.sent_messages[-limit:] if self.sent_messages else []
    
    def clear_history(self):
        """Clear message history"""
        self.sent_messages.clear()
        return True
    
    def get_provider_status(self):
        """Get status of all SMS providers"""
        status = {}
        
        for provider, config in self.providers.items():
            status[provider] = {
                'enabled': config.get('enabled', False),
                'quota': config.get('quota', 'Unknown')
            }
        
        return status
    
    def test_connectivity(self):
        """Test SMS connectivity"""
        test_results = {}
        
        # Test TextBelt
        try:
            response = requests.get('https://textbelt.com', timeout=10)
            test_results['textbelt'] = {
                'reachable': response.status_code == 200,
                'response_time': response.elapsed.total_seconds()
            }
        except:
            test_results['textbelt'] = {'reachable': False, 'error': 'Connection failed'}
        
        # Simulation always works
        test_results['simulation'] = {'reachable': True, 'response_time': 0.1}
        
        return test_results
    
    def get_stats(self):
        """Get SMS statistics"""
        if not self.sent_messages:
            return {
                'total_sent': 0,
                'success_rate': 0,
                'providers_used': []
            }
        
        total = len(self.sent_messages)
        successful = sum(1 for msg in self.sent_messages if msg['result'].get('success'))
        providers = list(set(msg['provider'] for msg in self.sent_messages))
        
        return {
            'total_sent': total,
            'successful': successful,
            'failed': total - successful,
            'success_rate': round((successful / total) * 100, 1) if total > 0 else 0,
            'providers_used': providers
        }
