import http.server
import socketserver
import urllib.parse
import subprocess
import json
import tkinter as tk
from tkinter import ttk, messagebox
from threading import Thread
import requests
import time
from datetime import datetime

PORT = 8000

USE_TOR = False
USE_VPN = False
TOR_PROXY = {'http': 'socks5://127.0.0.1:9050', 'https': 'socks5://127.0.0.1:9050'}
VPN_PROXY = {'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'}

class Handler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.path = '/index.html'
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

with socketserver.TCPServer(("", PORT), Handler) as httpd:
    print("serving at port", PORT)
    httpd.serve_forever()