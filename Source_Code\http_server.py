import http.server
import socketserver
import urllib.parse
import subprocess
import json
import tkinter as tk
from tkinter import messagebox
from threading import Thread
import time
import random

# Generate a random port every time
def get_random_port():
    # Use random port between 8000-9999 for better security
    return random.randint(8000, 9999)

def find_free_port():
    # Try random ports until we find a free one
    for _ in range(50):  # Try up to 50 random ports
        port = get_random_port()
        try:
            with socketserver.TCPServer(("", port), None):
                return port
        except OSError:
            continue
    # Fallback to sequential search if random fails
    for port in range(8000, 9999):
        try:
            with socketserver.TCPServer(("", port), None):
                return port
        except OSError:
            continue
    return 8000

PORT = find_free_port()

class Handler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            # Serve embedded HTML instead of file
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()

            html = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Blackhat Control System</title>
                <style>
                    body {{
                        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                        background: radial-gradient(circle at center, #0a0a0f 0%, #000000 100%);
                        color: #4a9eff;
                        min-height: 100vh;
                        margin: 0;
                        padding: 20px;
                    }}
                    .header {{
                        background: linear-gradient(135deg, #001122 0%, #002244 50%, #001122 100%);
                        padding: 25px 0;
                        text-align: center;
                        box-shadow: 0 8px 30px rgba(74, 158, 255, 0.3);
                        border-bottom: 2px solid #4a9eff;
                        margin-bottom: 30px;
                        border-radius: 12px;
                    }}
                    .header h1 {{
                        font-size: 3rem;
                        font-weight: 900;
                        color: #4a9eff;
                        text-shadow: 0 0 20px #4a9eff, 0 0 40px #4a9eff;
                        margin-bottom: 10px;
                        letter-spacing: 3px;
                        text-transform: uppercase;
                    }}
                    .container {{
                        max-width: 1200px;
                        margin: 0 auto;
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                        gap: 25px;
                    }}
                    .card {{
                        background: linear-gradient(135deg, #0d0d0d 0%, #001122 50%, #0d0d0d 100%);
                        border-radius: 12px;
                        padding: 25px;
                        box-shadow: 0 8px 32px rgba(74, 158, 255, 0.2);
                        border: 1px solid #4a9eff;
                    }}
                    .card-title {{
                        font-size: 1.3rem;
                        font-weight: 700;
                        color: #4a9eff;
                        margin-bottom: 20px;
                        text-transform: uppercase;
                    }}
                    .form-input {{
                        width: 100%;
                        padding: 12px 16px;
                        border: 2px solid #4a9eff;
                        border-radius: 8px;
                        background: #0a0a0a;
                        color: #7bb3ff;
                        font-size: 1rem;
                        margin: 10px 0;
                        box-sizing: border-box;
                    }}
                    .btn {{
                        padding: 12px 24px;
                        border: none;
                        border-radius: 8px;
                        font-size: 1rem;
                        font-weight: 600;
                        cursor: pointer;
                        background: linear-gradient(135deg, #4a9eff 0%, #2d7dd2 100%);
                        color: #ffffff;
                        margin: 5px;
                    }}
                    .console {{
                        background: #000000;
                        border: 2px solid #4a9eff;
                        border-radius: 8px;
                        padding: 15px;
                        margin-top: 15px;
                        min-height: 150px;
                        max-height: 200px;
                        overflow-y: auto;
                        font-family: monospace;
                        font-size: 0.9rem;
                        color: #4a9eff;
                    }}
                    .port-info {{
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        background: rgba(0, 0, 0, 0.8);
                        border: 1px solid #4a9eff;
                        border-radius: 8px;
                        padding: 10px;
                        font-family: monospace;
                        color: #00ff88;
                    }}
                </style>
            </head>
            <body>
                <div class="port-info">
                    🌐 Port: <span id="currentPort">{PORT}</span>
                </div>

                <div class="header">
                    <h1>⚠️ BLACKHAT CONTROL SYSTEM ⚠️</h1>
                    <p style="color: #7bb3ff; font-size: 1.2rem;">⚡ DYNAMIC PORT ROTATION • MAXIMUM STEALTH ⚡</p>
                </div>

                <div class="container">
                    <div class="card">
                        <div class="card-title">💀 STEALTH MESSAGING</div>

                        <div style="background: #003366; border: 2px solid #4a9eff; padding: 10px; border-radius: 8px; margin-bottom: 15px; text-align: center; color: #7bb3ff;">
                            🔒 STEALTH MODE ACTIVE - UNTRACEABLE OPERATIONS
                        </div>

                        <input type="text" class="form-input" id="phoneNumber" placeholder="Target Phone Number: +1234567890">
                        <textarea class="form-input" id="messageText" rows="3" placeholder="Message (Leave empty for silent Play Store popup)"></textarea>

                        <button class="btn" onclick="sendSMS()">📱 SEND ANONYMOUS SMS</button>

                        <div id="smsOutput" class="console">
                            <div style="color: #4a9eff;">💀 STEALTH MESSAGING SYSTEM ACTIVE...</div>
                            <div style="color: #7bb3ff;">⚠️ READY FOR ANONYMOUS OPERATIONS ⚠️</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">👁️ REMOTE SURVEILLANCE</div>

                        <input type="text" class="form-input" id="deviceId" placeholder="Device ID: target_device_001">
                        <button class="btn" onclick="takeScreenshot()">📸 CAPTURE SCREEN</button>

                        <div id="hvncOutput" class="console">
                            <div style="color: #4a9eff;">👁️ SURVEILLANCE SYSTEM ACTIVE...</div>
                            <div style="color: #7bb3ff;">⚠️ MONITORING ALL TARGETS ⚠️</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">🏴‍☠️ DATA EXTRACTION</div>

                        <button class="btn" onclick="harvestCrypto()">₿ HARVEST CRYPTO</button>
                        <button class="btn" onclick="harvestBanking()">🏦 HARVEST BANKING</button>

                        <div id="harvestOutput" class="console">
                            <div style="color: #4a9eff;">🏴‍☠️ DATA EXTRACTION PROTOCOLS LOADED...</div>
                            <div style="color: #7bb3ff;">⚠️ HARVESTING BANKING, CRYPTO & PERSONAL DATA ⚠️</div>
                        </div>
                    </div>
                </div>

                <script>
                    function getTimestamp() {{
                        return new Date().toLocaleTimeString();
                    }}

                    function getRandomElement(array) {{
                        return array[Math.floor(Math.random() * array.length)];
                    }}

                    function sendSMS() {{
                        const phone = document.getElementById('phoneNumber').value;
                        const message = document.getElementById('messageText').value;
                        const output = document.getElementById('smsOutput');
                        const timestamp = getTimestamp();
                        const devices = ['Android_001', 'iPhone_007', 'Samsung_Galaxy'];
                        const locations = ['New York', 'London', 'Berlin', 'Tokyo'];

                        if (!phone) {{
                            output.innerHTML += '<div style="color: #ff6666;">❌ Phone number required</div>';
                            return;
                        }}

                        if (!message) {{
                            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 📱 Silent deployment to ' + phone + '</div>';
                            output.innerHTML += '<div style="color: #7bb3ff;">🎯 Fake Play Store popup triggered</div>';
                            output.innerHTML += '<div style="color: #ffaa00;">⚠️ Location: ' + getRandomElement(locations) + ' - No SMS trace</div>';
                        }} else {{
                            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 📱 Anonymous SMS sent to ' + phone + '</div>';
                            output.innerHTML += '<div style="color: #7bb3ff;">💬 Message: ' + message + '</div>';
                            output.innerHTML += '<div style="color: #ffaa00;">🔥 Auto-delete in ' + Math.floor(Math.random() * 15 + 5) + ' seconds</div>';
                        }}

                        output.scrollTop = output.scrollHeight;
                    }}

                    function takeScreenshot() {{
                        const deviceId = document.getElementById('deviceId').value || 'unknown_device';
                        const output = document.getElementById('hvncOutput');
                        const timestamp = getTimestamp();

                        output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 📸 Screenshot captured from ' + deviceId + '</div>';
                        output.innerHTML += '<div style="color: #7bb3ff;">👁️ Remote surveillance active</div>';
                        output.innerHTML += '<div style="color: #ffaa00;">🔍 Screen monitoring enabled</div>';

                        output.scrollTop = output.scrollHeight;
                    }}

                    function harvestCrypto() {{
                        const output = document.getElementById('harvestOutput');
                        const timestamp = getTimestamp();
                        const platforms = ['Binance', 'Coinbase', 'Kraken', 'MetaMask'];
                        const amounts = ['2.5 BTC ($41,191)', '15.3 ETH ($28,567)', '450 BNB ($12,890)'];

                        output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] ₿ Crypto harvesting initiated...</div>';
                        output.innerHTML += '<div style="color: #7bb3ff;">💰 ' + getRandomElement(platforms) + ': ' + getRandomElement(amounts) + '</div>';
                        output.innerHTML += '<div style="color: #7bb3ff;">💰 ' + getRandomElement(platforms) + ': ' + getRandomElement(amounts) + '</div>';

                        output.scrollTop = output.scrollHeight;
                    }}

                    function harvestBanking() {{
                        const output = document.getElementById('harvestOutput');
                        const timestamp = getTimestamp();
                        const banks = ['Chase Bank: $234,567.89', 'HSBC: £45,123.67', 'Deutsche Bank: €78,901.23'];

                        output.innerHTML += `<div style="color: #00ff88;">[${timestamp}] 🏦 Banking data extraction...</div>`;
                        output.innerHTML += `<div style="color: #7bb3ff;">💳 ${getRandomElement(banks)}</div>`;
                        output.innerHTML += `<div style="color: #7bb3ff;">💳 ${getRandomElement(banks)}</div>`;

                        output.scrollTop = output.scrollHeight;
                    }}
                </script>
            </body>
            </html>
            """

            self.wfile.write(html.encode())
            return
        else:
            return http.server.SimpleHTTPRequestHandler.do_GET(self)

    def do_POST(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = urllib.parse.parse_qs(post_data.decode('utf-8'))

        response = {'success': True, 'message': 'Operation completed'}

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

def start_server():
    global PORT
    max_retries = 5

    for attempt in range(max_retries):
        try:
            # Create server with port reuse
            httpd = socketserver.TCPServer(("", PORT), Handler)
            httpd.allow_reuse_address = True

            print(f"🔵 Server started successfully on port {PORT}")
            print(f"🌐 Access: http://localhost:{PORT}")

            # Auto-open browser after short delay
            def open_browser():
                time.sleep(1)
                subprocess.run(['start', f'http://localhost:{PORT}'], shell=True)

            Thread(target=open_browser, daemon=True).start()

            # Start serving
            httpd.serve_forever()
            break

        except OSError as e:
            print(f"❌ Port {PORT} failed (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                PORT = find_free_port()
                print(f"🔄 Trying new port: {PORT}")
            else:
                print("❌ Failed to start server after multiple attempts")
                return
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return

def create_gui():
    global PORT
    root = tk.Tk()
    root.title("Blackhat Control System")
    root.geometry("450x350")
    root.configure(bg='#000000')

    title_label = tk.Label(
        root,
        text="🔵 BLACKHAT CONTROL SYSTEM",
        font=("Arial", 16, "bold"),
        fg="#4a9eff",
        bg="#000000"
    )
    title_label.pack(pady=20)

    # Show current port
    port_label = tk.Label(
        root,
        text=f"🌐 Current Port: {PORT}",
        font=("Arial", 12, "bold"),
        fg="#00ff88",
        bg="#000000"
    )
    port_label.pack(pady=5)

    # New port button
    def new_port():
        global PORT
        PORT = find_free_port()
        port_label.config(text=f"🌐 New Port: {PORT}")
        status_label.config(text=f"Port changed to {PORT} - Ready to start")

    new_port_btn = tk.Button(
        root,
        text="🔄 GENERATE NEW PORT",
        command=new_port,
        font=("Arial", 10, "bold"),
        bg="#ff6600",
        fg="#000000",
        relief="flat",
        padx=15,
        pady=8
    )
    new_port_btn.pack(pady=10)

    start_btn = tk.Button(
        root,
        text="🚀 START SERVER",
        command=lambda: Thread(target=start_server, daemon=True).start(),
        font=("Arial", 12, "bold"),
        bg="#4a9eff",
        fg="#000000",
        relief="flat",
        padx=20,
        pady=10
    )
    start_btn.pack(pady=15)

    open_btn = tk.Button(
        root,
        text="🌐 OPEN INTERFACE",
        command=lambda: subprocess.run(['start', f'http://localhost:{PORT}'], shell=True),
        font=("Arial", 12),
        bg="#7bb3ff",
        fg="#000000",
        relief="flat",
        padx=20,
        pady=10
    )
    open_btn.pack(pady=10)

    status_label = tk.Label(
        root,
        text=f"Ready to start on port {PORT}",
        font=("Arial", 10),
        fg="#7bb3ff",
        bg="#000000"
    )
    status_label.pack(pady=20)

    # Auto-start server after 2 seconds
    def auto_start():
        Thread(target=start_server, daemon=True).start()
        status_label.config(text=f"✅ Auto-started on port {PORT}")
        start_btn.config(text="✅ RUNNING", state="disabled")

    root.after(2000, auto_start)  # Auto-start after 2 seconds

    # Auto-generate new port every 30 seconds
    def auto_new_port():
        new_port()
        root.after(30000, auto_new_port)  # 30 seconds

    # Start auto port changing after server is running
    root.after(32000, auto_new_port)  # Start port rotation after server starts

    root.mainloop()

if __name__ == "__main__":
    create_gui()
