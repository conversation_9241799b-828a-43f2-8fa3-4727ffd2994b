#!/usr/bin/env python3
"""
BLACKHAT CONTROL SYSTEM
HTTP Server Component
"""

import http.server
import socketserver
import os
import json
import threading
import webbrowser
import urllib.parse
from config import SERVER_PORT, TOR_PROXY, VPN_PROXY
from license_manager import LicenseManager
from port_scanner import PortScanner

# Initialize license manager
license_manager = LicenseManager()

class RequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # Skip license verification
        if self.path == '/check_license':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'status': 'valid', 'message': 'License verification disabled'}
            self.wfile.write(json.dumps(response).encode())
            return
            
        # Serve static files
        if self.path == '/':
            self.path = '/index.html'
        return http.server.SimpleHTTPRequestHandler.do_GET(self)
    
    def do_POST(self):
        # Handle API requests
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
        except:
            data = urllib.parse.parse_qs(post_data.decode('utf-8'))
            # Convert lists to single values
            data = {k: v[0] for k, v in data.items()}
        
        # Skip license activation - always succeed
        if self.path == '/activate_license':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            # Always activate without checking serial
            license_manager.activate_license()
            
            response = {
                'status': 'success',
                'message': 'License activated successfully (verification disabled)'
            }
            self.wfile.write(json.dumps(response).encode())
            return
            
        # Process other API requests
        if self.path == '/api/status':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'status': 'active', 'anonymity': 'enabled'}
            self.wfile.write(json.dumps(response).encode())
        elif self.path == '/api/scan_ports':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            # Extract scan parameters
            target_ip = data.get('target_ip')
            device_type = data.get('device_type', 'android')
            service_type = data.get('service_type')
            use_tor = data.get('use_tor', False)
            use_vpn = data.get('use_vpn', False)
            scan_type = data.get('scan_type', 'device')
            
            # Initialize port scanner
            scanner = PortScanner(use_tor=use_tor, use_vpn=use_vpn)
            
            # Perform scan
            results = {}
            if scan_type == 'network':
                # Extract network prefix (first 3 octets)
                network_parts = target_ip.split('.')
                if len(network_parts) == 4:
                    network_prefix = '.'.join(network_parts[:3])
                    results = scanner.scan_network(network_prefix, device_type, service_type)
            else:
                # Scan single device
                results = scanner.scan_device(target_ip, device_type, service_type)
            
            # Return results
            response = {
                'status': 'success',
                'results': results
            }
            self.wfile.write(json.dumps(response).encode())
            return
        else:
            self.send_response(404)
            self.end_headers()

def start_server():
    """Start the HTTP server"""
    handler = RequestHandler
    httpd = socketserver.TCPServer(("", SERVER_PORT), handler)
    print(f"🔵 Server started at http://localhost:{SERVER_PORT}")
    httpd.serve_forever()

def open_browser():
    """Open browser after a short delay"""
    time.sleep(1)
    webbrowser.open(f"http://localhost:{SERVER_PORT}")

def main():
    # Check license (always passes with verification disabled)
    valid, message = license_manager.check_license()
    if not valid:
        print(f"❌ License error: {message}")
        return
    
    print("✅ License valid (verification disabled)")
    
    # Start server in a separate thread
    server_thread = threading.Thread(target=start_server)
    server_thread.daemon = True
    server_thread.start()
    
    # Open browser
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # Keep main thread running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🔵 Server shutdown")

if __name__ == "__main__":
    httpd.serve_forever()
