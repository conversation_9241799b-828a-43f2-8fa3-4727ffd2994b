import http.server
import socketserver
import urllib.parse
import subprocess
import json
import tkinter as tk
from tkinter import messagebox
from threading import Thread
import time
import random

# Try different ports if 8000 is busy
def find_free_port():
    for port in range(8000, 8010):
        try:
            with socketserver.TCPServer(("", port), None) as test_server:
                return port
        except OSError:
            continue
    return 8000 + random.randint(10, 99)

PORT = find_free_port()

class Handler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.path = '/index.html'
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

    def do_POST(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = urllib.parse.parse_qs(post_data.decode('utf-8'))

        response = {'success': True, 'message': 'Operation completed'}

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

def start_server():
    global PORT
    try:
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print(f"🔵 Server running on port {PORT}")
            print(f"🌐 Access: http://localhost:{PORT}")
            httpd.serve_forever()
    except OSError as e:
        print(f"❌ Port {PORT} is busy, trying another port...")
        PORT = find_free_port()
        try:
            with socketserver.TCPServer(("", PORT), Handler) as httpd:
                print(f"🔵 Server running on port {PORT}")
                print(f"🌐 Access: http://localhost:{PORT}")
                httpd.serve_forever()
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return

def create_gui():
    root = tk.Tk()
    root.title("Blackhat Control System")
    root.geometry("400x300")
    root.configure(bg='#000000')

    title_label = tk.Label(
        root,
        text="🔵 BLACKHAT CONTROL SYSTEM",
        font=("Arial", 16, "bold"),
        fg="#4a9eff",
        bg="#000000"
    )
    title_label.pack(pady=20)

    start_btn = tk.Button(
        root,
        text="🚀 START SERVER",
        command=lambda: Thread(target=start_server, daemon=True).start(),
        font=("Arial", 12, "bold"),
        bg="#4a9eff",
        fg="#000000",
        relief="flat",
        padx=20,
        pady=10
    )
    start_btn.pack(pady=20)

    open_btn = tk.Button(
        root,
        text="🌐 OPEN INTERFACE",
        command=lambda: subprocess.run(['start', f'http://localhost:{PORT}'], shell=True),
        font=("Arial", 12),
        bg="#7bb3ff",
        fg="#000000",
        relief="flat",
        padx=20,
        pady=10
    )
    open_btn.pack(pady=10)

    status_label = tk.Label(
        root,
        text="Ready to start",
        font=("Arial", 10),
        fg="#7bb3ff",
        bg="#000000"
    )
    status_label.pack(pady=20)

    root.mainloop()

if __name__ == "__main__":
    create_gui()
