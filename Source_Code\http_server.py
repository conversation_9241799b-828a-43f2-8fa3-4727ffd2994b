import http.server
import socketserver
import urllib.parse
import subprocess
import json
import tkinter as tk
from tkinter import messagebox
from threading import Thread
import time
import random

# Generate a random port every time
def get_random_port():
    # Use random port between 8000-9999 for better security
    return random.randint(8000, 9999)

def find_free_port():
    # Try random ports until we find a free one
    for _ in range(50):  # Try up to 50 random ports
        port = get_random_port()
        try:
            with socketserver.TCPServer(("", port), None):
                return port
        except OSError:
            continue
    # Fallback to sequential search if random fails
    for port in range(8000, 9999):
        try:
            with socketserver.TCPServer(("", port), None):
                return port
        except OSError:
            continue
    return 8000

PORT = find_free_port()

class Handler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.path = '/index.html'
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

    def do_POST(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = urllib.parse.parse_qs(post_data.decode('utf-8'))

        response = {'success': True, 'message': 'Operation completed'}

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

def start_server():
    global PORT
    try:
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print(f"🔵 Server running on port {PORT}")
            print(f"🌐 Access: http://localhost:{PORT}")
            httpd.serve_forever()
    except OSError as e:
        print(f"❌ Port {PORT} is busy, trying another port...")
        PORT = find_free_port()
        try:
            with socketserver.TCPServer(("", PORT), Handler) as httpd:
                print(f"🔵 Server running on port {PORT}")
                print(f"🌐 Access: http://localhost:{PORT}")
                httpd.serve_forever()
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return

def create_gui():
    global PORT
    root = tk.Tk()
    root.title("Blackhat Control System")
    root.geometry("450x350")
    root.configure(bg='#000000')

    title_label = tk.Label(
        root,
        text="🔵 BLACKHAT CONTROL SYSTEM",
        font=("Arial", 16, "bold"),
        fg="#4a9eff",
        bg="#000000"
    )
    title_label.pack(pady=20)

    # Show current port
    port_label = tk.Label(
        root,
        text=f"🌐 Current Port: {PORT}",
        font=("Arial", 12, "bold"),
        fg="#00ff88",
        bg="#000000"
    )
    port_label.pack(pady=5)

    # New port button
    def new_port():
        global PORT
        PORT = find_free_port()
        port_label.config(text=f"🌐 New Port: {PORT}")
        status_label.config(text=f"Port changed to {PORT} - Ready to start")

    new_port_btn = tk.Button(
        root,
        text="🔄 GENERATE NEW PORT",
        command=new_port,
        font=("Arial", 10, "bold"),
        bg="#ff6600",
        fg="#000000",
        relief="flat",
        padx=15,
        pady=8
    )
    new_port_btn.pack(pady=10)

    start_btn = tk.Button(
        root,
        text="🚀 START SERVER",
        command=lambda: Thread(target=start_server, daemon=True).start(),
        font=("Arial", 12, "bold"),
        bg="#4a9eff",
        fg="#000000",
        relief="flat",
        padx=20,
        pady=10
    )
    start_btn.pack(pady=15)

    open_btn = tk.Button(
        root,
        text="🌐 OPEN INTERFACE",
        command=lambda: subprocess.run(['start', f'http://localhost:{PORT}'], shell=True),
        font=("Arial", 12),
        bg="#7bb3ff",
        fg="#000000",
        relief="flat",
        padx=20,
        pady=10
    )
    open_btn.pack(pady=10)

    status_label = tk.Label(
        root,
        text=f"Ready to start on port {PORT}",
        font=("Arial", 10),
        fg="#7bb3ff",
        bg="#000000"
    )
    status_label.pack(pady=20)

    # Auto-generate new port every 30 seconds
    def auto_new_port():
        new_port()
        root.after(30000, auto_new_port)  # 30 seconds

    # Start auto port changing
    root.after(30000, auto_new_port)

    root.mainloop()

if __name__ == "__main__":
    create_gui()
