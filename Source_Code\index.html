<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BLACKHAT CONTROL SYSTEM</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #0a1929;
            color: #e6f1ff;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #061325;
            padding: 15px 0;
            border-bottom: 1px solid #1e3a5f;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #4dabf5;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .card {
            background-color: #132f4c;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-title {
            font-size: 18px;
            margin-bottom: 15px;
            color: #4dabf5;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-active {
            background-color: #4caf50;
        }
        .status-inactive {
            background-color: #f44336;
        }
        button {
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #1565c0;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">🔵 BLACKHAT CONTROL SYSTEM</div>
        </div>
    </header>
    
    <div class="container">
        <div class="dashboard">
            <div class="card">
                <div class="card-title">System Status</div>
                <div>
                    <span class="status-indicator status-active"></span>
                    System Active
                </div>
                <div>
                    <span class="status-indicator status-active"></span>
                    Server Running
                </div>
                <div>
                    <span class="status-indicator status-active"></span>
                    License Valid
                </div>
            </div>
            
            <div class="card">
                <div class="card-title">Anonymity Settings</div>
                <div>
                    <input type="checkbox" id="tor-proxy"> Use Tor Proxy
                </div>
                <div>
                    <input type="checkbox" id="vpn-proxy"> Use VPN Proxy
                </div>
                <div style="margin-top: 15px;">
                    <button id="apply-proxy">Apply Settings</button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-title">Target Platforms</div>
                <div>
                    <select id="platform-select" style="width: 100%; margin-bottom: 10px;">
                        <option value="">Select Platform</option>
                        <option value="crypto">Cryptocurrency Exchanges</option>
                        <option value="banking">Banking Platforms</option>
                        <option value="payment">Payment Services</option>
                    </select>
                </div>
                <div>
                    <button id="connect-platform">Connect</button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-title">Android Deployment</div>
                <div style="margin-bottom: 10px;">
                    <select id="apk-method" style="width: 100%;">
                        <option value="playstore">Fake Play Store Update</option>
                        <option value="direct">Direct APK Download</option>
                        <option value="silent">Silent Installation</option>
                    </select>
                </div>
                <div>
                    <button id="deploy-apk">Deploy</button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-title">Mass SMS System</div>
                <div style="margin-bottom: 10px;">
                    <label for="sms-template">Template:</label>
                    <select id="sms-template" style="width: 100%;">
                        <option value="update">App Update</option>
                        <option value="verification">Verification Code</option>
                        <option value="alert">Security Alert</option>
                        <option value="password">Password Reset</option>
                        <option value="custom">Custom Message</option>
                    </select>
                </div>
                <div style="margin-bottom: 10px;">
                    <label for="sms-message">Message:</label>
                    <textarea id="sms-message" style="width: 100%; height: 80px;"></textarea>
                </div>
                <div style="margin-bottom: 10px;">
                    <label for="sms-numbers">Phone Numbers (comma separated):</label>
                    <textarea id="sms-numbers" style="width: 100%; height: 60px;"></textarea>
                </div>
                <div style="margin-bottom: 10px;">
                    <label for="sms-link">Link:</label>
                    <input type="text" id="sms-link" style="width: 100%;">
                </div>
                <div style="margin-bottom: 10px;">
                    <input type="checkbox" id="sms-tor"> Use Tor
                    <input type="checkbox" id="sms-vpn"> Use VPN
                </div>
                <div>
                    <button id="send-single-sms">Send Single</button>
                    <button id="send-mass-sms">Send Mass SMS</button>
                </div>
            </div>
            <div class="card">
                <div class="card-title">Mobile Port Scanner</div>
                <div class="form-group">
                    <label for="target-ip">Target IP or Network:</label>
                    <input type="text" id="target-ip" placeholder="*********** or *************">
                </div>
                <div class="form-group">
                    <label for="device-type">Device Type:</label>
                    <select id="device-type">
                        <option value="android">Android</option>
                        <option value="ios">iOS</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="service-type">Service Type (Optional):</label>
                    <select id="service-type">
                        <option value="">All Services</option>
                        <option value="banking">Banking</option>
                        <option value="payment">Payment</option>
                        <option value="crypto">Cryptocurrency</option>
                        <option value="messaging">Messaging</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Anonymity:</label>
                    <div class="checkbox-group">
                        <input type="checkbox" id="scan-tor" checked>
                        <label for="scan-tor">Use Tor</label>
                        <input type="checkbox" id="scan-vpn">
                        <label for="scan-vpn">Use VPN</label>
                    </div>
                </div>
                <div class="form-group">
                    <label for="scan-type">Scan Type:</label>
                    <select id="scan-type">
                        <option value="device">Single Device</option>
                        <option value="network">Network Range</option>
                    </select>
                </div>
                <div>
                    <button id="start-scan">Start Scan</button>
                </div>
                <div class="results-container">
                    <div class="results-title">Scan Results:</div>
                    <div id="scan-results" class="results-box"></div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Basic interface functionality
        document.getElementById('apply-proxy').addEventListener('click', function() {
            const torEnabled = document.getElementById('tor-proxy').checked;
            const vpnEnabled = document.getElementById('vpn-proxy').checked;
            
            // Apply proxy settings
            console.log('Proxy settings updated:', { tor: torEnabled, vpn: vpnEnabled });
            alert('Anonymity settings applied successfully');
        });
        
        document.getElementById('connect-platform').addEventListener('click', function() {
            const platform = document.getElementById('platform-select').value;
            if (!platform) {
                alert('Please select a platform');
                return;
            }
            
            // Connect to selected platform
            console.log('Connecting to platform:', platform);
            alert('Connected to ' + platform + ' platforms');
        });
        
        document.getElementById('deploy-apk').addEventListener('click', function() {
            const method = document.getElementById('apk-method').value;
            
            // Deploy APK using selected method
            console.log('Deploying APK using method:', method);
            alert('APK deployment initiated using ' + method + ' method');
        });
        
        // SMS Template handling
        document.getElementById('sms-template').addEventListener('change', function() {
            const template = this.value;
            const messageField = document.getElementById('sms-message');
            
            if (template === 'update') {
                messageField.value = 'Your {app} needs to be updated for security reasons. Download: {link}';
            } else if (template === 'verification') {
                messageField.value = 'Your verification code is: {code}';
            } else if (template === 'alert') {
                messageField.value = 'Security alert: Your {account} requires immediate verification: {link}';
            } else if (template === 'password') {
                messageField.value = 'Your {service} password has been reset. Verify now: {link}';
            } else {
                messageField.value = '';
            }
        });
        
        // Send single SMS
        document.getElementById('send-single-sms').addEventListener('click', function() {
            const message = document.getElementById('sms-message').value;
            const numbers = document.getElementById('sms-numbers').value.split(',')[0].trim();
            const link = document.getElementById('sms-link').value;
            const useTor = document.getElementById('sms-tor').checked;
            const useVpn = document.getElementById('sms-vpn').checked;
            
            if (!numbers || !message) {
                alert('Please enter a phone number and message');
                return;
            }
            
            // Prepare data
            const data = {
                phone: numbers,
                message: message,
                link: link,
                app: 'Google Play Store',
                account: 'online account',
                service: 'online service',
                code: Math.floor(100000 + Math.random() * 900000).toString(),
                use_tor: useTor,
                use_vpn: useVpn
            };
            
            // Send request
            fetch('/api/send_sms', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('SMS sent successfully');
                } else {
                    alert('Failed to send SMS: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error: ' + error);
            });
        });
        
        // Send mass SMS
        document.getElementById('send-mass-sms').addEventListener('click', function() {
            const message = document.getElementById('sms-message').value;
            const numbers = document.getElementById('sms-numbers').value;
            const link = document.getElementById('sms-link').value;
            const useTor = document.getElementById('sms-tor').checked;
            const useVpn = document.getElementById('sms-vpn').checked;
            
            if (!numbers || !message) {
                alert('Please enter phone numbers and message');
                return;
            }
            
            // Prepare data
            const data = {
                phones: numbers,
                message: message,
                link: link,
                app: 'Google Play Store',
                account: 'online account',
                service: 'online service',
                code: Math.floor(100000 + Math.random() * 900000).toString(),
                use_tor: useTor,
                use_vpn: useVpn
            };
            
            // Confirm mass sending
            const phoneCount = numbers.split(',').length;
            if (!confirm(`Are you sure you want to send SMS to ${phoneCount} numbers?`)) {
                return;
            }
            
            // Send request
            fetch('/api/send_mass_sms', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Mass SMS sending started. Total: ${data.total} numbers`);
                } else {
                    alert('Failed to start mass SMS: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error: ' + error);
            });
        });
        document.getElementById('start-scan').addEventListener('click', function() {
            const targetIp = document.getElementById('target-ip').value;
            const deviceType = document.getElementById('device-type').value;
            const serviceType = document.getElementById('service-type').value;
            const useTor = document.getElementById('scan-tor').checked;
            const useVpn = document.getElementById('scan-vpn').checked;
            const scanType = document.getElementById('scan-type').value;
            
            if (!targetIp) {
                alert('Please enter a target IP or network');
                return;
            }
            
            // Show loading indicator
            const resultsBox = document.getElementById('scan-results');
            resultsBox.innerHTML = '<div class="loading">Scanning... This may take a few minutes.</div>';
            
            // Prepare data for API request
            const data = {
                target_ip: targetIp,
                device_type: deviceType,
                service_type: serviceType || null,
                use_tor: useTor,
                use_vpn: useVpn,
                scan_type: scanType
            };
            
            // Send scan request to server
            fetch('/api/scan_ports', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                // Display results
                if (Object.keys(data.results).length === 0) {
                    resultsBox.innerHTML = '<div class="no-results">No open ports found.</div>';
                    return;
                }
                
                let resultsHtml = '';
                for (const ip in data.results) {
                    const ports = data.results[ip].join(', ');
                    resultsHtml += `<div class="result-item">
                        <div class="result-ip">${ip}</div>
                        <div class="result-ports">Open Ports: ${ports}</div>
                    </div>`;
                }
                
                resultsBox.innerHTML = resultsHtml;
            })
            .catch(error => {
                resultsBox.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            });
        });
    </script>
</body>
            background: radial-gradient(circle at center, </html>
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, #0a1a2a, #1a3a5a);
            background: radial-gradient(circle at center, </html>
