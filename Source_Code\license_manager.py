
"""
License Management System (Serial Verification Disabled)
"""
import hashlib
import base64
import time
import json
import os
import uuid
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# Sample valid serial codes (not used when verification is disabled)
VALID_SERIALS = [
    "A1B2-C3D4-E5F6-7890",
    "B2C3-D4E5-F6A7-8901",
    "C3D4-E5F6-A7B8-9012"
]

class LicenseManager:
    def __init__(self):
        self.license_file = "license.dat"
        self.key_salt = b'blackhat_control_system_2024'
        self.verification_disabled = True  # Serial verification disabled
        
    def generate_key(self, password):
        """Generate encryption key from password"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.key_salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return Fernet(key)
        
    def get_machine_id(self):
        """Get unique machine identifier"""
        machine_id = str(uuid.getnode())
        return hashlib.md5(machine_id.encode()).hexdigest()
            
    def activate_license(self, serial=None):
        """Activate license without serial verification"""
        # Create license data with 30-day validity regardless of serial
        expiry_date = datetime.now() + timedelta(days=30)
        license_data = {
            'serial': serial or "DISABLED-VERIFICATION",
            'machine_id': self.get_machine_id(),
            'activation_date': datetime.now().isoformat(),
            'expiry': expiry_date.isoformat()
        }
        
        # Encrypt license data
        json_data = json.dumps(license_data).encode()
        cipher = self.generate_key(self.get_machine_id())
        encrypted_data = cipher.encrypt(json_data)
        
        # Save encrypted license data
        with open(self.license_file, 'wb') as f:
            f.write(encrypted_data)
            
        return True
    
    def check_license(self):
        """Check if license is valid (always returns true when verification is disabled)"""
        if self.verification_disabled:
            # If verification is disabled, create a new license if one doesn't exist
            if not os.path.exists(self.license_file):
                self.activate_license()
            return True, "License verification disabled"
            
        # Normal license checking logic (not used when verification is disabled)
        if not os.path.exists(self.license_file):
            return False, "No license found"
            
        try:
            with open(self.license_file, 'rb') as f:
                encrypted_data = f.read()
                
            cipher = self.generate_key(self.get_machine_id())
            json_data = cipher.decrypt(encrypted_data)
            license_data = json.loads(json_data.decode())
            
            # Check if license is for this machine
            if license_data['machine_id'] != self.get_machine_id():
                return False, "License not valid for this machine"
                
            # Check if license is expired
            expiry = datetime.fromisoformat(license_data['expiry'])
            if expiry < datetime.now():
                return False, "License expired"
                
            return True, "License valid"
        except Exception as e:
            return False, f"Error checking license: {str(e)}"

