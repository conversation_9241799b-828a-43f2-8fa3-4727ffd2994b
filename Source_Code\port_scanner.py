"""
Mobile Device Port Scanner Module
"""
import socket
import threading
import time
import random
import queue
from config import M<PERSON><PERSON><PERSON>_TARGET_PORTS, MOBILE_SERVICES, TOR_PROXY, VPN_PROXY

class PortScanner:
    def __init__(self, use_tor=False, use_vpn=False):
        """Initialize port scanner with anonymity options"""
        self.use_tor = use_tor
        self.use_vpn = use_vpn
        self.timeout = 1.0  # Default timeout in seconds
        self.results = {}
        self.scan_queue = queue.Queue()
        self.max_threads = 10
        self.active_threads = 0
        self.lock = threading.Lock()
        
    def scan_port(self, target_ip, port):
        """Scan a single port on target IP"""
        try:
            # Create socket
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(self.timeout)
            
            # Connect to target port
            result = s.connect_ex((target_ip, port))
            s.close()
            
            # Port is open if result is 0
            if result == 0:
                with self.lock:
                    if target_ip not in self.results:
                        self.results[target_ip] = []
                    self.results[target_ip].append(port)
                return True
            return False
        except:
            return False
    
    def worker(self):
        """Worker thread to process scan queue"""
        while not self.scan_queue.empty():
            try:
                # Get next item from queue
                target_ip, port = self.scan_queue.get(block=False)
                
                # Scan port
                self.scan_port(target_ip, port)
                
                # Random delay for stealth
                if self.use_tor or self.use_vpn:
                    time.sleep(random.uniform(0.1, 0.5))
                
                # Mark task as done
                self.scan_queue.task_done()
            except queue.Empty:
                break
            except Exception as e:
                print(f"Error in worker: {e}")
                self.scan_queue.task_done()
        
        # Decrease active thread count
        with self.lock:
            self.active_threads -= 1
    
    def scan_device(self, target_ip, device_type="android", service_type=None):
        """Scan a device for open ports"""
        # Clear previous results
        self.results = {}
        
        # Select ports to scan
        ports_to_scan = []
        
        # Add device-specific ports
        if device_type.lower() in MOBILE_TARGET_PORTS:
            ports_to_scan.extend(MOBILE_TARGET_PORTS[device_type.lower()])
        
        # Add service-specific ports if requested
        if service_type and service_type.lower() in MOBILE_SERVICES:
            ports_to_scan.extend(MOBILE_SERVICES[service_type.lower()])
        
        # Remove duplicates
        ports_to_scan = list(set(ports_to_scan))
        
        # Add all port/IP combinations to queue
        for port in ports_to_scan:
            self.scan_queue.put((target_ip, port))
        
        # Start worker threads
        self.active_threads = min(self.max_threads, len(ports_to_scan))
        for _ in range(self.active_threads):
            thread = threading.Thread(target=self.worker)
            thread.daemon = True
            thread.start()
        
        # Wait for all tasks to complete
        self.scan_queue.join()
        
        # Return results
        return self.results
    
    def scan_network(self, network_prefix, device_type="android", service_type=None):
        """Scan a network range for devices with open ports"""
        # Clear previous results
        self.results = {}
        
        # Generate IP addresses in the network range (last octet 1-254)
        for i in range(1, 255):
            target_ip = f"{network_prefix}.{i}"
            
            # Select ports to scan
            ports_to_scan = []
            
            # Add device-specific ports
            if device_type.lower() in MOBILE_TARGET_PORTS:
                ports_to_scan.extend(MOBILE_TARGET_PORTS[device_type.lower()])
            
            # Add service-specific ports if requested
            if service_type and service_type.lower() in MOBILE_SERVICES:
                ports_to_scan.extend(MOBILE_SERVICES[service_type.lower()])
            
            # Remove duplicates
            ports_to_scan = list(set(ports_to_scan))
            
            # Add all port/IP combinations to queue
            for port in ports_to_scan:
                self.scan_queue.put((target_ip, port))
        
        # Start worker threads
        self.active_threads = self.max_threads
        for _ in range(self.active_threads):
            thread = threading.Thread(target=self.worker)
            thread.daemon = True
            thread.start()
        
        # Wait for all tasks to complete
        self.scan_queue.join()
        
        # Return results
        return self.results

