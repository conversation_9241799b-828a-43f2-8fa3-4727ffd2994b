#!/usr/bin/env python3
"""
BLACKHAT CONTROL SYSTEM
Professional Stealth Interface
"""

import subprocess
import sys
import os

def check_requirements():
    """Check if required packages are installed"""
    try:
        import requests
        print("✓ Basic requirements satisfied")
        
        # Install all requirements from file
        current_dir = os.path.dirname(os.path.abspath(__file__))
        req_path = os.path.join(current_dir, "requirements.txt")
        
        if os.path.exists(req_path):
            print("Installing all requirements...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", req_path])
                print("✓ All requirements installed")
            except Exception as e:
                print(f"⚠️ Warning: Could not install all requirements: {e}")
                print("Some features may be limited.")
        
        return True
    except ImportError:
        print("❌ Missing basic requirements")
        print("Installing required packages...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
            print("✓ Basic requirements installed")
            return True
        except:
            print("❌ Failed to install requirements")
            print("Please run: pip install requests")
            return False

def create_directories():
    """Create necessary directories if they don't exist"""
    directories = ["logs", "data", "configs", "apk", "apk_configs", "reports"]
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    for directory in directories:
        dir_path = os.path.join(current_dir, "..", directory)
        if not os.path.exists(dir_path):
            print(f"Creating directory: {directory}")
            os.makedirs(dir_path, exist_ok=True)

def main():
    print("🔵 BLACKHAT CONTROL SYSTEM")
    print("⚡ Professional Stealth Interface")
    print("=" * 40)
    
    # Create necessary directories
    create_directories()
    
    if not check_requirements():
        return
    
    print("\n🚀 Starting control system...")
    
    try:
        # Start the main server
        current_dir = os.path.dirname(os.path.abspath(__file__))
        http_server_path = os.path.join(current_dir, "http_server.py")
        subprocess.run([sys.executable, http_server_path])
    except KeyboardInterrupt:
        print("\n🔵 System shutdown")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()


