#!/usr/bin/env python3
"""
BLACKHAT CONTROL SYSTEM
Main Launcher
"""
import sys
import os
import time
import threading
from PyQt5.QtWidgets import QApplication, QSplashScreen, QProgressBar
from PyQt5.QtGui import QPixmap, QColor, Q<PERSON>ainter
from PyQt5.QtCore import Qt, QTimer

from blackhat_control_gui import BlackhatControlGUI

def show_splash():
    """Show splash screen while loading"""
    # Create splash screen
    splash_pix = QPixmap(400, 200)
    splash_pix.fill(QColor(10, 25, 45))
    
    # Add text to splash screen
    painter = QPainter(splash_pix)
    painter.setPen(Qt.white)
    painter.setFont(Qt.QFont("Arial", 16, Qt.QFont.Bold))
    painter.drawText(splash_pix.rect(), Qt.Align<PERSON>enter, "BLACKHAT CONTROL SYSTEM\nLoading...")
    painter.end()
    
    splash = QSplashScreen(splash_pix)
    
    # Add progress bar
    progress = QProgressBar(splash)
    progress.setGeometry(20, 150, 360, 20)
    progress.setStyleSheet("""
        QProgressBar {
            border: 1px solid #3A6EA5;
            border-radius: 4px;
            text-align: center;
            background-color: #0A192D;
        }
        QProgressBar::chunk {
            background-color: #3A6EA5;
        }
    """)
    
    # Show splash screen
    splash.show()
    
    # Simulate loading
    for i in range(101):
        progress.setValue(i)
        QApplication.processEvents()
        time.sleep(0.02)
    
    return splash

def main():
    """Main function"""
    # Create application
    app = QApplication(sys.argv)
    
    # Show splash screen
    splash = show_splash()
    
    # Create main window
    window = BlackhatControlGUI()
    
    # Close splash screen and show main window
    splash.finish(window)
    
    # Start application event loop
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()