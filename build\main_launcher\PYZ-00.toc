('C:\\Users\\<USER>\\Desktop\\Silentsms\\build\\main_launcher\\PYZ-00.pyz',
 [('__future__',
   'C:\\Program Files\\Python310\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python310\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python310\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python310\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python310\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python310\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Program Files\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program Files\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program Files\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program Files\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program Files\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program Files\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program Files\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program Files\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program Files\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program Files\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program Files\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program Files\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program Files\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program Files\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program Files\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program Files\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program Files\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program Files\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program Files\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program Files\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program Files\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program Files\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program Files\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program Files\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python310\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python310\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python310\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python310\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program Files\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python310\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python310\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python310\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.fernet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\fernet.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.pbkdf2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\pbkdf2.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python310\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python310\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python310\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python310\\lib\\decimal.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python310\\lib\\dis.py', 'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python310\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python310\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python310\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python310\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python310\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python310\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python310\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python310\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python310\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python310\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python310\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python310\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python310\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python310\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python310\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Program Files\\Python310\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Program Files\\Python310\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'C:\\Program Files\\Python310\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python310\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program Files\\Python310\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program Files\\Python310\\lib\\http\\server.py',
   'PYMODULE'),
  ('http_server',
   'C:\\Users\\<USER>\\Desktop\\Silentsms\\http_server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._adapters',
   'C:\\Program Files\\Python310\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'C:\\Program Files\\Python310\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python310\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python310\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python310\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'C:\\Program Files\\Python310\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python310\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python310\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python310\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('license_manager',
   'C:\\Users\\<USER>\\Desktop\\Silentsms\\license_manager.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python310\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python310\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python310\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'C:\\Program Files\\Python310\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python310\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python310\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python310\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python310\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python310\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python310\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python310\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python310\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python310\\lib\\py_compile.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python310\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python310\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python310\\lib\\random.py', 'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files\\Python310\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Program Files\\Python310\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python310\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python310\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python310\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python310\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python310\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'C:\\Program Files\\Python310\\lib\\socketserver.py',
   'PYMODULE'),
  ('socks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\socks.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python310\\lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python310\\lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python310\\lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python310\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files\\Python310\\lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python310\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python310\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python310\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python310\\lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'C:\\Program Files\\Python310\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Program Files\\Python310\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Program Files\\Python310\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Program Files\\Python310\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Program Files\\Python310\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Program Files\\Python310\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'C:\\Program Files\\Python310\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python310\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python310\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python310\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python310\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python310\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu', 'C:\\Program Files\\Python310\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'C:\\Program Files\\Python310\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'C:\\Program Files\\Python310\\lib\\webbrowser.py',
   'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python310\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files\\Python310\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'C:\\Program Files\\Python310\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python310\\lib\\zipimport.py', 'PYMODULE'),
  ('zstandard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
