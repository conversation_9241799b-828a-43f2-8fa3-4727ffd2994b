"""
Custom SMS Panel Implementation
"""
import os
import json
import time
import threading
import queue
import logging
from datetime import datetime
from flask import Flask, request, jsonify, render_template, redirect, url_for
from flask_httpauth import HTTP<PERSON>asic<PERSON>uth
from werkzeug.security import generate_password_hash, check_password_hash

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sms_panel.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SMS_PANEL")

# Initialize Flask app
app = Flask(__name__)
auth = HTTPBasicAuth()

# Configuration
SMS_QUEUE_FILE = "sms_queue.json"
SMS_LOGS_FILE = "sms_logs.json"
USERS_FILE = "panel_users.json"
API_KEYS_FILE = "api_keys.json"

# In-memory storage
sms_queue = queue.Queue()
sms_logs = []
users = {}
api_keys = {}

# Load data from files
def load_data():
    global sms_logs, users, api_keys
    
    # Load SMS logs
    if os.path.exists(SMS_LOGS_FILE):
        try:
            with open(SMS_LOGS_FILE, 'r') as f:
                sms_logs = json.load(f)
        except:
            sms_logs = []
    
    # Load users
    if os.path.exists(USERS_FILE):
        try:
            with open(USERS_FILE, 'r') as f:
                users_data = json.load(f)
                users = {u["username"]: u for u in users_data}
        except:
            # Create default admin user if no users exist
            users = {
                "admin": {
                    "username": "admin",
                    "password_hash": generate_password_hash("admin123"),
                    "role": "admin"
                }
            }
            save_users()
    else:
        # Create default admin user
        users = {
            "admin": {
                "username": "admin",
                "password_hash": generate_password_hash("admin123"),
                "role": "admin"
            }
        }
        save_users()
    
    # Load API keys
    if os.path.exists(API_KEYS_FILE):
        try:
            with open(API_KEYS_FILE, 'r') as f:
                api_keys_data = json.load(f)
                api_keys = {k["api_key"]: k for k in api_keys_data}
        except:
            api_keys = {}

# Save data to files
def save_sms_logs():
    with open(SMS_LOGS_FILE, 'w') as f:
        json.dump(sms_logs, f, indent=2)

def save_users():
    with open(USERS_FILE, 'w') as f:
        json.dump(list(users.values()), f, indent=2)

def save_api_keys():
    with open(API_KEYS_FILE, 'w') as f:
        json.dump(list(api_keys.values()), f, indent=2)

# SMS processing thread
def sms_processor():
    while True:
        try:
            # Get SMS from queue
            sms_data = sms_queue.get(timeout=1)
            
            # Process SMS (in a real system, this would connect to SMS gateway)
            phone = sms_data["phone"]
            message = sms_data["message"]
            sender = sms_data.get("sender", "INFO")
            
            # Simulate sending delay
            time.sleep(0.5)
            
            # Generate message ID
            message_id = f"{int(time.time())}-{len(sms_logs)}"
            
            # Log the SMS
            log_entry = {
                "id": message_id,
                "phone": phone,
                "message": message,
                "sender": sender,
                "status": "SENT",
                "timestamp": datetime.now().isoformat(),
                "user": sms_data.get("user", "system")
            }
            
            sms_logs.append(log_entry)
            
            # Save logs periodically
            if len(sms_logs) % 10 == 0:
                save_sms_logs()
            
            # Mark task as done
            sms_queue.task_done()
            
            logger.info(f"SMS sent to {phone}: {message[:20]}...")
            
        except queue.Empty:
            # No SMS in queue, wait a bit
            time.sleep(0.1)
        except Exception as e:
            logger.error(f"Error processing SMS: {str(e)}")
            time.sleep(1)

# Authentication
@auth.verify_password
def verify_password(username, password):
    if username in users and check_password_hash(users[username]["password_hash"], password):
        return username
    return None

# API key verification
def verify_api_key(api_key):
    return api_key in api_keys

# Routes
@app.route('/')
@auth.login_required
def index():
    return render_template('panel.html', 
                          user=users[auth.current_user()],
                          sms_count=len(sms_logs),
                          queue_size=sms_queue.qsize())

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username in users and check_password_hash(users[username]["password_hash"], password):
            # In a real app, you'd use proper session management
            return redirect('/')
        
        return render_template('login.html', error="Invalid credentials")
    
    return render_template('login.html')

@app.route('/api/send', methods=['POST'])
def api_send():
    # Check API key
    api_key = request.headers.get('X-API-Key')
    if not api_key or not verify_api_key(api_key):
        return jsonify({"success": False, "error": "Invalid API key"}), 401
    
    data = request.json
    
    # Validate request
    if not data or 'phone' not in data or 'message' not in data:
        return jsonify({"success": False, "error": "Missing required fields"}), 400
    
    # Add to queue
    sms_data = {
        "phone": data["phone"],
        "message": data["message"],
        "sender": data.get("sender", "INFO"),
        "user": api_keys[api_key]["owner"]
    }
    
    sms_queue.put(sms_data)
    
    return jsonify({
        "success": True,
        "message": "SMS queued for sending",
        "queue_position": sms_queue.qsize()
    })

@app.route('/api/bulk-send', methods=['POST'])
def api_bulk_send():
    # Check API key
    api_key = request.headers.get('X-API-Key')
    if not api_key or not verify_api_key(api_key):
        return jsonify({"success": False, "error": "Invalid API key"}), 401
    
    data = request.json
    
    # Validate request
    if not data or 'phones' not in data or 'message' not in data:
        return jsonify({"success": False, "error": "Missing required fields"}), 400
    
    if not isinstance(data["phones"], list):
        return jsonify({"success": False, "error": "Phones must be a list"}), 400
    
    # Add to queue
    count = 0
    for phone in data["phones"]:
        sms_data = {
            "phone": phone,
            "message": data["message"],
            "sender": data.get("sender", "INFO"),
            "user": api_keys[api_key]["owner"]
        }
        
        sms_queue.put(sms_data)
        count += 1
    
    return jsonify({
        "success": True,
        "message": f"{count} SMS messages queued for sending",
        "count": count
    })

@app.route('/api/status', methods=['GET'])
def api_status():
    # Check API key
    api_key = request.headers.get('X-API-Key')
    if not api_key or not verify_api_key(api_key):
        return jsonify({"success": False, "error": "Invalid API key"}), 401
    
    return jsonify({
        "success": True,
        "queue_size": sms_queue.qsize(),
        "total_sent": len(sms_logs),
        "status": "online"
    })

@app.route('/admin/users', methods=['GET', 'POST'])
@auth.login_required
def admin_users():
    # Check if admin
    if users[auth.current_user()]["role"] != "admin":
        return redirect('/')
    
    if request.method == 'POST':
        action = request.form.get('action')
        
        if action == 'add':
            username = request.form.get('username')
            password = request.form.get('password')
            role = request.form.get('role', 'user')
            
            if username and password and username not in users:
                users[username] = {
                    "username": username,
                    "password_hash": generate_password_hash(password),
                    "role": role
                }
                save_users()
        
        elif action == 'delete':
            username = request.form.get('username')
            if username in users and username != 'admin':
                del users[username]
                save_users()
    
    return render_template('users.html', 
                          users=users.values(),
                          user=users[auth.current_user()])

@app.route('/admin/api-keys', methods=['GET', 'POST'])
@auth.login_required
def admin_api_keys():
    # Check if admin
    if users[auth.current_user()]["role"] != "admin":
        return redirect('/')
    
    if request.method == 'POST':
        action = request.form.get('action')
        
        if action == 'add':
            name = request.form.get('name')
            owner = request.form.get('owner', auth.current_user())
            
            if name:
                # Generate API key
                import uuid
                api_key = str(uuid.uuid4())
                
                api_keys[api_key] = {
                    "api_key": api_key,
                    "name": name,
                    "owner": owner,
                    "created": datetime.now().isoformat()
                }
                save_api_keys()
        
        elif action == 'delete':
            key = request.form.get('api_key')
            if key in api_keys:
                del api_keys[key]
                save_api_keys()
    
    return render_template('api_keys.html', 
                          api_keys=api_keys.values(),
                          user=users[auth.current_user()])

@app.route('/logs')
@auth.login_required
def view_logs():
    page = int(request.args.get('page', 1))
    per_page = 50
    start = (page - 1) * per_page
    end = start + per_page
    
    # Get logs for current page
    page_logs = sms_logs[start:end]
    total_pages = (len(sms_logs) + per_page - 1) // per_page
    
    return render_template('logs.html', 
                          logs=page_logs,
                          page=page,
                          total_pages=total_pages,
                          user=users[auth.current_user()])

# Start SMS processor thread
def start_processor():
    thread = threading.Thread(target=sms_processor)
    thread.daemon = True
    thread.start()

# Main function
def main():
    # Load data
    load_data()
    
    # Start SMS processor
    start_processor()
    
    # Start Flask app
    app.run(host='0.0.0.0', port=5000, debug=False)

if __name__ == "__main__":
    main()