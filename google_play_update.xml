<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <ImageView
            android:id="@+id/playLogo"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/google_play_icon"
            android:contentDescription="Google Play Logo" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Google Play Store"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#202124"
            android:layout_marginStart="12dp" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0" />

    <TextView
        android:id="@+id/updateText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Google Play Store needs to be updated to version 32.4.18-21"
        android:textSize="16sp"
        android:textColor="#202124"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="This update includes critical security fixes and performance improvements."
        android:textSize="14sp"
        android:textColor="#5F6368"
        android:layout_marginBottom="16dp" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:max="100"
        android:progress="0" />

    <TextView
        android:id="@+id/statusText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text=""
        android:textSize="14