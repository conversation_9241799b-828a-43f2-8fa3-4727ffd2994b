#!/usr/bin/env python3
"""
Simple GUI test to verify tkin<PERSON> is working
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import threading
import time

def test_gui():
    """Test if GUI appears"""
    try:
        root = tk.Tk()
        root.title("GUI Test")
        root.geometry("500x400")
        root.configure(bg='#000000')
        
        # Make sure window appears on top
        root.lift()
        root.attributes('-topmost', True)
        root.after_idle(root.attributes, '-topmost', False)
        
        # Title
        title = tk.Label(
            root,
            text="🔵 GUI TEST WINDOW",
            font=("Arial", 20, "bold"),
            fg="#4a9eff",
            bg="#000000"
        )
        title.pack(pady=30)
        
        # Status
        status = tk.Label(
            root,
            text="✅ GUI is working correctly!",
            font=("Arial", 14),
            fg="#00ff88",
            bg="#000000"
        )
        status.pack(pady=20)
        
        # Instructions
        instructions = tk.Label(
            root,
            text="If you can see this window, the GUI is working.\nClick the button below to test the main system.",
            font=("Arial", 12),
            fg="#7bb3ff",
            bg="#000000",
            justify="center"
        )
        instructions.pack(pady=20)
        
        # Test button
        def run_main():
            try:
                subprocess.Popen(['python', 'Source_Code/http_server.py'])
                messagebox.showinfo("Success", "Main system started!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to start main system: {e}")
        
        test_btn = tk.Button(
            root,
            text="🚀 START MAIN SYSTEM",
            command=run_main,
            font=("Arial", 14, "bold"),
            bg="#4a9eff",
            fg="#000000",
            padx=30,
            pady=15
        )
        test_btn.pack(pady=30)
        
        # Close button
        close_btn = tk.Button(
            root,
            text="❌ CLOSE",
            command=root.quit,
            font=("Arial", 12),
            bg="#ff6666",
            fg="#000000",
            padx=20,
            pady=10
        )
        close_btn.pack(pady=10)
        
        # Debug info
        debug = tk.Label(
            root,
            text=f"Python GUI Test\nTkinter version: {tk.TkVersion}",
            font=("Arial", 10),
            fg="#666666",
            bg="#000000"
        )
        debug.pack(pady=20)
        
        print("GUI window created, starting mainloop...")
        root.mainloop()
        print("GUI mainloop ended")
        
    except Exception as e:
        print(f"GUI Error: {e}")
        # Fallback - show console message
        print("=" * 50)
        print("GUI FAILED TO START")
        print("=" * 50)
        print("Trying to start main system directly...")
        try:
            subprocess.run(['python', 'Source_Code/http_server.py'])
        except Exception as e2:
            print(f"Main system also failed: {e2}")

if __name__ == "__main__":
    print("Starting GUI test...")
    test_gui()
