#!/usr/bin/env python3
"""
Simple GUI Launcher for Blackhat Control System
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import os
import sys

def start_gui():
    """Start the main GUI"""
    try:
        # Change to Source_Code directory
        os.chdir('Source_Code')
        
        # Run the http_server.py
        subprocess.run([sys.executable, 'http_server.py'], check=True)
        
    except FileNotFoundError:
        messagebox.showerror("Error", "Source_Code/http_server.py not found!")
    except Exception as e:
        messagebox.showerror("Error", f"Failed to start: {str(e)}")

def create_launcher():
    """Create the launcher window"""
    root = tk.Tk()
    root.title("Blackhat Control System - Launcher")
    root.geometry("500x300")
    root.configure(bg='#000000')
    
    # Title
    title_label = tk.Label(
        root,
        text="🔵 BLACKHAT CONTROL SYSTEM",
        font=("Arial", 20, "bold"),
        fg="#4a9eff",
        bg="#000000"
    )
    title_label.pack(pady=30)
    
    # Subtitle
    subtitle_label = tk.Label(
        root,
        text="⚡ STEALTH OPERATIONS LAUNCHER ⚡",
        font=("Arial", 12),
        fg="#7bb3ff",
        bg="#000000"
    )
    subtitle_label.pack(pady=10)

    # Feature info
    feature_label = tk.Label(
        root,
        text="🔄 DYNAMIC PORT ROTATION • 🛡️ MAXIMUM ANONYMITY",
        font=("Arial", 10, "bold"),
        fg="#00ff88",
        bg="#000000"
    )
    feature_label.pack(pady=5)
    
    # Start button
    start_btn = tk.Button(
        root,
        text="🚀 START CONTROL SYSTEM",
        command=start_gui,
        font=("Arial", 14, "bold"),
        bg="#4a9eff",
        fg="#000000",
        relief="flat",
        padx=30,
        pady=15,
        cursor="hand2"
    )
    start_btn.pack(pady=40)
    
    # Status
    status_label = tk.Label(
        root,
        text="Ready to launch stealth operations with random port",
        font=("Arial", 10),
        fg="#7bb3ff",
        bg="#000000"
    )
    status_label.pack(pady=20)

    # Instructions
    instructions = tk.Label(
        root,
        text="• Port changes automatically every 30 seconds\n• New random port on each startup\n• Maximum stealth and anonymity",
        font=("Arial", 9),
        fg="#666666",
        bg="#000000",
        justify="left"
    )
    instructions.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    create_launcher()
