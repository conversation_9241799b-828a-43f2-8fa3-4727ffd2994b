#!/usr/bin/env python3
"""
Simple GUI Launcher for Blackhat Control System
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import os
import sys

def start_gui():
    """Start both online and offline GUI"""
    try:
        # Start offline control panel first
        subprocess.Popen([sys.executable, 'offline_control_panel.py'])

        # Wait a moment then start online version
        import time
        time.sleep(1)

        # Change to Source_Code directory and start web server
        os.chdir('Source_Code')
        subprocess.Popen([sys.executable, 'http_server.py'])

        messagebox.showinfo("Success", "Both offline panel and web interface started!\n\n✅ Offline GUI: Full control panel\n✅ Online GUI: Web browser interface")

    except FileNotFoundError:
        messagebox.showerror("Error", "Required files not found!")
    except Exception as e:
        messagebox.showerror("Error", f"Failed to start: {str(e)}")

def create_launcher():
    """Create the launcher window"""
    root = tk.Tk()
    root.title("Blackhat Control System - Launcher")
    root.geometry("500x300")
    root.configure(bg='#000000')
    
    # Title
    title_label = tk.Label(
        root,
        text="🔵 BLACKHAT CONTROL SYSTEM",
        font=("Arial", 20, "bold"),
        fg="#4a9eff",
        bg="#000000"
    )
    title_label.pack(pady=30)
    
    # Subtitle
    subtitle_label = tk.Label(
        root,
        text="⚡ STEALTH OPERATIONS LAUNCHER ⚡",
        font=("Arial", 12),
        fg="#7bb3ff",
        bg="#000000"
    )
    subtitle_label.pack(pady=10)

    # Feature info
    feature_label = tk.Label(
        root,
        text="🖥️ OFFLINE GUI + 🌐 ONLINE WEB • 🔄 DYNAMIC PORTS • 🛡️ MAXIMUM ANONYMITY",
        font=("Arial", 10, "bold"),
        fg="#00ff88",
        bg="#000000"
    )
    feature_label.pack(pady=5)
    
    # Start button
    start_btn = tk.Button(
        root,
        text="🚀 START DUAL CONTROL SYSTEM",
        command=start_gui,
        font=("Arial", 14, "bold"),
        bg="#4a9eff",
        fg="#000000",
        relief="flat",
        padx=30,
        pady=15,
        cursor="hand2"
    )
    start_btn.pack(pady=20)

    # Individual launch buttons
    button_frame = tk.Frame(root, bg="#000000")
    button_frame.pack(pady=10)

    offline_btn = tk.Button(
        button_frame,
        text="🖥️ OFFLINE ONLY",
        command=lambda: subprocess.Popen([sys.executable, 'offline_control_panel.py']),
        font=("Arial", 11, "bold"),
        bg="#00ff88",
        fg="#000000",
        relief="flat",
        padx=20,
        pady=10
    )
    offline_btn.pack(side='left', padx=10)

    online_btn = tk.Button(
        button_frame,
        text="🌐 WEB ONLY",
        command=lambda: [os.chdir('Source_Code'), subprocess.Popen([sys.executable, 'http_server.py'])],
        font=("Arial", 11, "bold"),
        bg="#ff6600",
        fg="#000000",
        relief="flat",
        padx=20,
        pady=10
    )
    online_btn.pack(side='left', padx=10)
    
    # Status
    status_label = tk.Label(
        root,
        text="Ready to launch dual interface system",
        font=("Arial", 10),
        fg="#7bb3ff",
        bg="#000000"
    )
    status_label.pack(pady=15)

    # Instructions
    instructions = tk.Label(
        root,
        text="🚀 DUAL SYSTEM: Offline GUI + Web Interface\n🖥️ OFFLINE ONLY: Just the GUI control panel\n🌐 WEB ONLY: Just the browser interface\n\n• Port changes automatically every 30 seconds\n• Maximum stealth and anonymity",
        font=("Arial", 9),
        fg="#666666",
        bg="#000000",
        justify="center"
    )
    instructions.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    create_launcher()
