#!/usr/bin/env python3
"""
Offline Blackhat Control Panel - No Browser Required
Complete GUI-based control system
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import random
import time
from datetime import datetime
from threading import Thread
import subprocess

class BlackhatControlPanel:
    def __init__(self):
        self.current_port = random.randint(8000, 9999)
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.start_port_rotation()
        
    def setup_window(self):
        self.root.title("🔵 BLACKHAT CONTROL SYSTEM - OFFLINE MODE")
        self.root.geometry("1200x800")
        self.root.configure(bg='#000000')
        self.root.resizable(True, True)
        
        # Make window appear on top
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(self.root.attributes, '-topmost', False)
        
    def create_widgets(self):
        # Main title
        title_frame = tk.Frame(self.root, bg='#000000')
        title_frame.pack(fill='x', pady=10)
        
        title = tk.Label(
            title_frame,
            text="⚠️ BLACKHAT CONTROL SYSTEM ⚠️",
            font=("Arial", 24, "bold"),
            fg="#4a9eff",
            bg="#000000"
        )
        title.pack()
        
        subtitle = tk.Label(
            title_frame,
            text="⚡ OFFLINE MODE • DYNAMIC PORT ROTATION • MAXIMUM STEALTH ⚡",
            font=("Arial", 12, "bold"),
            fg="#7bb3ff",
            bg="#000000"
        )
        subtitle.pack(pady=5)
        
        # Port display
        self.port_label = tk.Label(
            title_frame,
            text=f"🌐 Current Port: {self.current_port}",
            font=("Arial", 14, "bold"),
            fg="#00ff88",
            bg="#000000"
        )
        self.port_label.pack(pady=5)
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#000000')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Create three columns
        self.create_sms_panel(main_frame)
        self.create_surveillance_panel(main_frame)
        self.create_extraction_panel(main_frame)
        
    def create_sms_panel(self, parent):
        # SMS Panel
        sms_frame = tk.LabelFrame(
            parent,
            text="💀 STEALTH MESSAGING",
            font=("Arial", 14, "bold"),
            fg="#4a9eff",
            bg="#001122",
            bd=2,
            relief="solid"
        )
        sms_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew", ipadx=10, ipady=10)
        
        # Status indicator
        status_frame = tk.Frame(sms_frame, bg="#003366", relief="solid", bd=2)
        status_frame.pack(fill='x', pady=10)
        
        self.sms_status = tk.Label(
            status_frame,
            text="🔒 STEALTH MODE ACTIVE - UNTRACEABLE OPERATIONS",
            font=("Arial", 10, "bold"),
            fg="#7bb3ff",
            bg="#003366"
        )
        self.sms_status.pack(pady=5)
        
        # Phone number input
        tk.Label(sms_frame, text="Target Phone Number:", font=("Arial", 10, "bold"), fg="#7bb3ff", bg="#001122").pack(anchor='w', pady=(10,2))
        self.phone_entry = tk.Entry(sms_frame, font=("Arial", 12), bg="#0a0a0a", fg="#7bb3ff", insertbackground="#7bb3ff")
        self.phone_entry.pack(fill='x', pady=2)
        self.phone_entry.insert(0, "+1234567890")
        
        # Message input
        tk.Label(sms_frame, text="Message (Leave empty for silent Play Store popup):", font=("Arial", 10, "bold"), fg="#7bb3ff", bg="#001122").pack(anchor='w', pady=(10,2))
        self.message_text = tk.Text(sms_frame, height=4, font=("Arial", 10), bg="#0a0a0a", fg="#7bb3ff", insertbackground="#7bb3ff")
        self.message_text.pack(fill='x', pady=2)
        
        # Send button
        send_btn = tk.Button(
            sms_frame,
            text="📱 SEND ANONYMOUS SMS",
            command=self.send_sms,
            font=("Arial", 12, "bold"),
            bg="#4a9eff",
            fg="#000000",
            relief="flat",
            padx=20,
            pady=10
        )
        send_btn.pack(pady=10)
        
        # Output console
        tk.Label(sms_frame, text="Console Output:", font=("Arial", 10, "bold"), fg="#7bb3ff", bg="#001122").pack(anchor='w', pady=(10,2))
        self.sms_output = scrolledtext.ScrolledText(
            sms_frame,
            height=8,
            font=("Consolas", 9),
            bg="#000000",
            fg="#4a9eff",
            insertbackground="#4a9eff"
        )
        self.sms_output.pack(fill='both', expand=True, pady=2)
        self.sms_output.insert('end', "💀 STEALTH MESSAGING SYSTEM ACTIVE...\n⚠️ READY FOR ANONYMOUS OPERATIONS ⚠️\n")
        
    def create_surveillance_panel(self, parent):
        # Surveillance Panel
        surv_frame = tk.LabelFrame(
            parent,
            text="👁️ REMOTE SURVEILLANCE",
            font=("Arial", 14, "bold"),
            fg="#4a9eff",
            bg="#001122",
            bd=2,
            relief="solid"
        )
        surv_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew", ipadx=10, ipady=10)
        
        # Device ID input
        tk.Label(surv_frame, text="Device ID:", font=("Arial", 10, "bold"), fg="#7bb3ff", bg="#001122").pack(anchor='w', pady=(10,2))
        self.device_entry = tk.Entry(surv_frame, font=("Arial", 12), bg="#0a0a0a", fg="#7bb3ff", insertbackground="#7bb3ff")
        self.device_entry.pack(fill='x', pady=2)
        self.device_entry.insert(0, "target_device_001")
        
        # Control buttons
        btn_frame = tk.Frame(surv_frame, bg="#001122")
        btn_frame.pack(fill='x', pady=10)
        
        screenshot_btn = tk.Button(
            btn_frame,
            text="📸 CAPTURE SCREEN",
            command=self.take_screenshot,
            font=("Arial", 11, "bold"),
            bg="#4a9eff",
            fg="#000000",
            relief="flat",
            padx=15,
            pady=8
        )
        screenshot_btn.pack(side='left', padx=5)
        
        freeze_btn = tk.Button(
            btn_frame,
            text="❄️ FREEZE SCREEN",
            command=self.freeze_screen,
            font=("Arial", 11, "bold"),
            bg="#ff6600",
            fg="#000000",
            relief="flat",
            padx=15,
            pady=8
        )
        freeze_btn.pack(side='left', padx=5)
        
        # Output console
        tk.Label(surv_frame, text="Surveillance Log:", font=("Arial", 10, "bold"), fg="#7bb3ff", bg="#001122").pack(anchor='w', pady=(10,2))
        self.surv_output = scrolledtext.ScrolledText(
            surv_frame,
            height=12,
            font=("Consolas", 9),
            bg="#000000",
            fg="#4a9eff",
            insertbackground="#4a9eff"
        )
        self.surv_output.pack(fill='both', expand=True, pady=2)
        self.surv_output.insert('end', "👁️ SURVEILLANCE SYSTEM ACTIVE...\n⚠️ MONITORING ALL TARGETS ⚠️\n")
        
    def create_extraction_panel(self, parent):
        # Data Extraction Panel
        extract_frame = tk.LabelFrame(
            parent,
            text="🏴‍☠️ DATA EXTRACTION",
            font=("Arial", 14, "bold"),
            fg="#4a9eff",
            bg="#001122",
            bd=2,
            relief="solid"
        )
        extract_frame.grid(row=0, column=2, padx=10, pady=10, sticky="nsew", ipadx=10, ipady=10)
        
        # Control buttons
        btn_frame = tk.Frame(extract_frame, bg="#001122")
        btn_frame.pack(fill='x', pady=10)
        
        crypto_btn = tk.Button(
            btn_frame,
            text="₿ HARVEST CRYPTO",
            command=self.harvest_crypto,
            font=("Arial", 12, "bold"),
            bg="#4a9eff",
            fg="#000000",
            relief="flat",
            padx=20,
            pady=10
        )
        crypto_btn.pack(fill='x', pady=5)
        
        banking_btn = tk.Button(
            btn_frame,
            text="🏦 HARVEST BANKING",
            command=self.harvest_banking,
            font=("Arial", 12, "bold"),
            bg="#4a9eff",
            fg="#000000",
            relief="flat",
            padx=20,
            pady=10
        )
        banking_btn.pack(fill='x', pady=5)
        
        cookies_btn = tk.Button(
            btn_frame,
            text="🍪 EXTRACT COOKIES",
            command=self.extract_cookies,
            font=("Arial", 12, "bold"),
            bg="#4a9eff",
            fg="#000000",
            relief="flat",
            padx=20,
            pady=10
        )
        cookies_btn.pack(fill='x', pady=5)
        
        # Output console
        tk.Label(extract_frame, text="Extraction Log:", font=("Arial", 10, "bold"), fg="#7bb3ff", bg="#001122").pack(anchor='w', pady=(10,2))
        self.extract_output = scrolledtext.ScrolledText(
            extract_frame,
            height=10,
            font=("Consolas", 9),
            bg="#000000",
            fg="#4a9eff",
            insertbackground="#4a9eff"
        )
        self.extract_output.pack(fill='both', expand=True, pady=2)
        self.extract_output.insert('end', "🏴‍☠️ DATA EXTRACTION PROTOCOLS LOADED...\n⚠️ HARVESTING BANKING, CRYPTO & PERSONAL DATA ⚠️\n")
        
        # Configure grid weights
        parent.grid_columnconfigure(0, weight=1)
        parent.grid_columnconfigure(1, weight=1)
        parent.grid_columnconfigure(2, weight=1)
        parent.grid_rowconfigure(0, weight=1)
        
    def get_timestamp(self):
        return datetime.now().strftime("%H:%M:%S")
    
    def get_random_element(self, array):
        return random.choice(array)
    
    def send_sms(self):
        phone = self.phone_entry.get()
        message = self.message_text.get("1.0", "end-1c")
        timestamp = self.get_timestamp()
        
        devices = ['Android_001', 'iPhone_007', 'Samsung_Galaxy', 'Pixel_Device']
        locations = ['New York', 'London', 'Berlin', 'Tokyo', 'Sydney', 'Toronto']
        
        if not phone:
            self.sms_output.insert('end', f"❌ Phone number required\n")
            self.sms_output.see('end')
            return
        
        if not message.strip():
            self.sms_output.insert('end', f"[{timestamp}] 📱 Silent deployment to {phone}\n")
            self.sms_output.insert('end', f"🎯 Fake Play Store popup triggered via {self.get_random_element(devices)}\n")
            self.sms_output.insert('end', f"⚠️ Location spoofed: {self.get_random_element(locations)} - No SMS trace\n")
            self.sms_output.insert('end', f"✅ APK payload delivered successfully\n\n")
        else:
            self.sms_output.insert('end', f"[{timestamp}] 📱 Anonymous SMS sent to {phone}\n")
            self.sms_output.insert('end', f"💬 Message: {message}\n")
            self.sms_output.insert('end', f"🔥 Auto-delete in {random.randint(5, 20)} seconds\n")
            self.sms_output.insert('end', f"📍 Routed through: {self.get_random_element(locations)}\n\n")
        
        self.sms_output.see('end')
        
    def take_screenshot(self):
        device_id = self.device_entry.get() or self.get_random_element(['Android_001', 'iPhone_007', 'Samsung_Galaxy'])
        timestamp = self.get_timestamp()
        locations = ['New York', 'London', 'Berlin', 'Tokyo', 'Sydney']
        resolution = f"{random.randint(1080, 1440)}x{random.randint(1920, 2560)}"
        
        self.surv_output.insert('end', f"[{timestamp}] 📸 Screenshot captured from {device_id}\n")
        self.surv_output.insert('end', f"👁️ Resolution: {resolution} | Location: {self.get_random_element(locations)}\n")
        self.surv_output.insert('end', f"🔍 Screen monitoring active - {random.randint(10, 50)} apps detected\n")
        self.surv_output.insert('end', f"💾 Image saved: screenshot_{int(time.time())}.png\n\n")
        self.surv_output.see('end')
        
    def freeze_screen(self):
        device_id = self.device_entry.get() or 'unknown_device'
        timestamp = self.get_timestamp()
        
        self.surv_output.insert('end', f"[{timestamp}] ❄️ Screen freeze activated on {device_id}\n")
        self.surv_output.insert('end', f"🚫 User input blocked for {random.randint(30, 120)} seconds\n")
        self.surv_output.insert('end', f"⚡ Takeover mode enabled\n\n")
        self.surv_output.see('end')
        
    def harvest_crypto(self):
        timestamp = self.get_timestamp()
        platforms = [
            {'name': 'Binance', 'crypto': 'BTC', 'amount': f"{random.uniform(1.5, 5.2):.2f}"},
            {'name': 'Coinbase', 'crypto': 'ETH', 'amount': f"{random.uniform(8.3, 25.7):.1f}"},
            {'name': 'Kraken', 'crypto': 'BTC', 'amount': f"{random.uniform(0.8, 3.1):.2f}"},
            {'name': 'MetaMask', 'crypto': 'BNB', 'amount': f"{random.randint(200, 800)}"}
        ]
        
        selected = random.sample(platforms, 3)
        
        self.extract_output.insert('end', f"[{timestamp}] ₿ Crypto harvesting initiated...\n")
        
        for platform in selected:
            usd_value = random.randint(20000, 80000)
            self.extract_output.insert('end', f"💰 {platform['name']}: {platform['amount']} {platform['crypto']} (${usd_value:,})\n")
        
        total_value = sum(random.randint(20000, 80000) for _ in selected)
        self.extract_output.insert('end', f"💎 Total extracted: ${total_value:,}\n\n")
        self.extract_output.see('end')
        
    def harvest_banking(self):
        timestamp = self.get_timestamp()
        banks = [
            {'name': 'Chase Bank', 'country': 'USA', 'currency': 'USD', 'symbol': '$'},
            {'name': 'HSBC', 'country': 'UK', 'currency': 'GBP', 'symbol': '£'},
            {'name': 'Deutsche Bank', 'country': 'Germany', 'currency': 'EUR', 'symbol': '€'},
            {'name': 'BNP Paribas', 'country': 'France', 'currency': 'EUR', 'symbol': '€'}
        ]
        
        selected = random.sample(banks, 3)
        
        self.extract_output.insert('end', f"[{timestamp}] 🏦 Banking data extraction...\n")
        
        for bank in selected:
            amount = random.randint(50000, 500000)
            self.extract_output.insert('end', f"💳 {bank['name']} ({bank['country']}): {bank['symbol']}{amount:,}\n")
        
        accounts = random.randint(5, 25)
        self.extract_output.insert('end', f"🔐 {accounts} accounts compromised\n\n")
        self.extract_output.see('end')
        
    def extract_cookies(self):
        timestamp = self.get_timestamp()
        sites = ['amazon.com', 'paypal.com', 'facebook.com', 'google.com', 'microsoft.com', 'apple.com']
        
        self.extract_output.insert('end', f"[{timestamp}] 🍪 Cookie extraction initiated...\n")
        
        for site in random.sample(sites, 4):
            cookies = random.randint(15, 150)
            self.extract_output.insert('end', f"🔓 {site}: {cookies} cookies extracted\n")
        
        self.extract_output.insert('end', f"📊 Session tokens harvested\n\n")
        self.extract_output.see('end')
        
    def start_port_rotation(self):
        def rotate_port():
            self.current_port = random.randint(8000, 9999)
            self.port_label.config(text=f"🌐 Current Port: {self.current_port}")
            
            # Update status messages
            status_messages = [
                "🔒 STEALTH MODE ACTIVE - UNTRACEABLE OPERATIONS",
                "⚡ MAXIMUM ANONYMITY - ALL SYSTEMS OPERATIONAL", 
                "🛡️ ENCRYPTED CHANNELS - ZERO DETECTION RISK",
                "🌐 TOR ROUTING ACTIVE - COMPLETE INVISIBILITY"
            ]
            self.sms_status.config(text=self.get_random_element(status_messages))
            
            # Schedule next rotation
            self.root.after(30000, rotate_port)  # 30 seconds
        
        # Start first rotation after 30 seconds
        self.root.after(30000, rotate_port)
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = BlackhatControlPanel()
    app.run()
