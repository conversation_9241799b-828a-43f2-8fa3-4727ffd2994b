@echo off
echo 🔵 BLACKHAT CONTROL SYSTEM - FILE ORGANIZER
echo =======================================

REM Create destination folder
set DEST_FOLDER=BlackhatControl_Consolidated
echo Creating %DEST_FOLDER% folder...
mkdir %DEST_FOLDER% 2>nul

REM Copy main Python files
echo Copying main Python files...
copy blackhat_control_gui.py %DEST_FOLDER%\ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Copied blackhat_control_gui.py
) else (
    echo ⚠️ Failed to copy blackhat_control_gui.py
)

copy sms_panel_connector.py %DEST_FOLDER%\ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Copied sms_panel_connector.py
) else (
    echo ⚠️ Failed to copy sms_panel_connector.py
)

REM Copy configuration files
echo Copying configuration files...
copy Source_Code\config.py %DEST_FOLDER%\ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Copied config.py
) else (
    echo ⚠️ Failed to copy config.py
)

REM Copy startup files
echo Copying startup files...
copy start.bat %DEST_FOLDER%\ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Copied start.bat
) else (
    echo ⚠️ Failed to copy start.bat
)

copy Source_Code\start.py %DEST_FOLDER%\ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Copied start.py
) else (
    echo ⚠️ Failed to copy start.py
)

REM Copy documentation files
echo Copying documentation files...
copy BlackhatControl_Complete\README.md %DEST_FOLDER%\ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Copied README.md
) else (
    echo ⚠️ Failed to copy README.md
)

copy BlackhatControl_Final\INSTALL.md %DEST_FOLDER%\ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Copied INSTALL.md
) else (
    echo ⚠️ Failed to copy INSTALL.md
)

copy BlackhatControl_Complete\QUICK_START.txt %DEST_FOLDER%\ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Copied QUICK_START.txt
) else (
    echo ⚠️ Failed to copy QUICK_START.txt
)

REM Create necessary subdirectories
echo Creating necessary subdirectories...
mkdir %DEST_FOLDER%\logs 2>nul
mkdir %DEST_FOLDER%\data 2>nul
mkdir %DEST_FOLDER%\configs 2>nul
mkdir %DEST_FOLDER%\apk 2>nul
mkdir %DEST_FOLDER%\apk_configs 2>nul
mkdir %DEST_FOLDER%\reports 2>nul

REM Copy the start_system.bat file
echo @echo off > %DEST_FOLDER%\start_system.bat
echo echo 🔵 BLACKHAT CONTROL SYSTEM >> %DEST_FOLDER%\start_system.bat
echo echo ⚡ Professional Stealth Interface >> %DEST_FOLDER%\start_system.bat
echo echo ======================================= >> %DEST_FOLDER%\start_system.bat
echo. >> %DEST_FOLDER%\start_system.bat
echo REM Check if Python is installed >> %DEST_FOLDER%\start_system.bat
echo python --version ^>nul 2^>^&1 >> %DEST_FOLDER%\start_system.bat
echo if %%errorlevel%% neq 0 ( >> %DEST_FOLDER%\start_system.bat
echo     echo ❌ Python not found. Please install Python 3.7 or higher. >> %DEST_FOLDER%\start_system.bat
echo     echo    Download from: https://www.python.org/downloads/ >> %DEST_FOLDER%\start_system.bat
echo     pause >> %DEST_FOLDER%\start_system.bat
echo     exit /b 1 >> %DEST_FOLDER%\start_system.bat
echo ) >> %DEST_FOLDER%\start_system.bat
echo. >> %DEST_FOLDER%\start_system.bat
echo REM Check Python version >> %DEST_FOLDER%\start_system.bat
echo for /f "tokens=2" %%%%a in ('python --version 2^^^^>^^^^&1') do set PYVER=%%%%a >> %DEST_FOLDER%\start_system.bat
echo echo ✓ Python %%PYVER%% detected >> %DEST_FOLDER%\start_system.bat
echo. >> %DEST_FOLDER%\start_system.bat
echo REM Create necessary directories if they don't exist >> %DEST_FOLDER%\start_system.bat
echo echo Checking directories... >> %DEST_FOLDER%\start_system.bat
echo if not exist logs mkdir logs ^&^& echo ✓ Created logs directory >> %DEST_FOLDER%\start_system.bat
echo if not exist data mkdir data ^&^& echo ✓ Created data directory >> %DEST_FOLDER%\start_system.bat
echo if not exist configs mkdir configs ^&^& echo ✓ Created configs directory >> %DEST_FOLDER%\start_system.bat
echo if not exist apk mkdir apk ^&^& echo ✓ Created apk directory >> %DEST_FOLDER%\start_system.bat
echo if not exist apk_configs mkdir apk_configs ^&^& echo ✓ Created apk_configs directory >> %DEST_FOLDER%\start_system.bat
echo if not exist reports mkdir reports ^&^& echo ✓ Created reports directory >> %DEST_FOLDER%\start_system.bat
echo. >> %DEST_FOLDER%\start_system.bat
echo REM Install required packages >> %DEST_FOLDER%\start_system.bat
echo echo Installing required packages... >> %DEST_FOLDER%\start_system.bat
echo pip install requests cryptography flask flask-httpauth PyQt5 waitress gunicorn >> %DEST_FOLDER%\start_system.bat
echo. >> %DEST_FOLDER%\start_system.bat
echo REM Check for Tor proxy >> %DEST_FOLDER%\start_system.bat
echo echo Checking Tor proxy... >> %DEST_FOLDER%\start_system.bat
echo curl --socks5 127.0.0.1:9050 https://check.torproject.org/ ^>nul 2^>^&1 >> %DEST_FOLDER%\start_system.bat
echo if %%errorlevel%% equ 0 ( >> %DEST_FOLDER%\start_system.bat
echo     echo ✓ Tor proxy detected >> %DEST_FOLDER%\start_system.bat
echo ) else ( >> %DEST_FOLDER%\start_system.bat
echo     echo ⚠️ Tor proxy not detected. Some features may be limited. >> %DEST_FOLDER%\start_system.bat
echo ) >> %DEST_FOLDER%\start_system.bat
echo. >> %DEST_FOLDER%\start_system.bat
echo REM Check for VPN proxy >> %DEST_FOLDER%\start_system.bat
echo echo Checking VPN proxy... >> %DEST_FOLDER%\start_system.bat
echo curl --proxy 127.0.0.1:8080 https://api.ipify.org/ ^>nul 2^>^&1 >> %DEST_FOLDER%\start_system.bat
echo if %%errorlevel%% equ 0 ( >> %DEST_FOLDER%\start_system.bat
echo     echo ✓ VPN proxy detected >> %DEST_FOLDER%\start_system.bat
echo ) else ( >> %DEST_FOLDER%\start_system.bat
echo     echo ⚠️ VPN proxy not detected. Some features may be limited. >> %DEST_FOLDER%\start_system.bat
echo ) >> %DEST_FOLDER%\start_system.bat
echo. >> %DEST_FOLDER%\start_system.bat
echo REM Start the SMS Panel Connector >> %DEST_FOLDER%\start_system.bat
echo echo Starting SMS Panel Connector... >> %DEST_FOLDER%\start_system.bat
echo start "SMS Panel" cmd /c "python sms_panel_connector.py" >> %DEST_FOLDER%\start_system.bat
echo echo ✓ SMS Panel started on http://localhost:5000 >> %DEST_FOLDER%\start_system.bat
echo. >> %DEST_FOLDER%\start_system.bat
echo REM Start the main GUI application >> %DEST_FOLDER%\start_system.bat
echo echo Starting Blackhat Control GUI... >> %DEST_FOLDER%\start_system.bat
echo start "Blackhat Control" cmd /c "python blackhat_control_gui.py" >> %DEST_FOLDER%\start_system.bat
echo echo ✓ GUI application started >> %DEST_FOLDER%\start_system.bat
echo. >> %DEST_FOLDER%\start_system.bat
echo echo. >> %DEST_FOLDER%\start_system.bat
echo echo 🚀 BLACKHAT CONTROL SYSTEM STARTED SUCCESSFULLY >> %DEST_FOLDER%\start_system.bat
echo echo ======================================= >> %DEST_FOLDER%\start_system.bat
echo echo ✓ SMS Panel: http://localhost:5000 >> %DEST_FOLDER%\start_system.bat
echo echo ✓ Main GUI: Running in separate window >> %DEST_FOLDER%\start_system.bat
echo echo. >> %DEST_FOLDER%\start_system.bat
echo echo Press any key to exit this console (system will continue running)... >> %DEST_FOLDER%\start_system.bat
echo pause ^>nul >> %DEST_FOLDER%\start_system.bat

echo.
echo ✅ All files have been consolidated in the '%DEST_FOLDER%' folder
echo ✅ Created start_system.bat in the '%DEST_FOLDER%' folder
echo.
echo To start the system:
echo 1. Navigate to the '%DEST_FOLDER%' folder
echo 2. Run start_system.bat
echo.
echo Press any key to exit...
pause >nul