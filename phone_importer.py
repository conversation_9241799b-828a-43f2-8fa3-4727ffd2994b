"""
Phone Number Importer Utility
"""
import csv
import json
import re

def validate_phone(phone):
    """Basic phone number validation"""
    # Remove non-digit characters
    clean_phone = re.sub(r'\D', '', phone)
    
    # Check if it's a valid length (adjust as needed)
    if len(clean_phone) < 10 or len(clean_phone) > 15:
        return False, clean_phone
    
    return True, clean_phone

def import_from_csv(file_path, column_name=None, column_index=0):
    """Import phone numbers from CSV file"""
    phone_numbers = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            headers = next(reader, None)
            
            # Determine column to use
            col_idx = column_index
            if headers and column_name:
                try:
                    col_idx = headers.index(column_name)
                except ValueError:
                    pass
            
            # Read phone numbers
            for row in reader:
                if len(row) > col_idx:
                    phone = row[col_idx].strip()
                    valid, clean_phone = validate_phone(phone)
                    if valid:
                        phone_numbers.append(clean_phone)
    
    except Exception as e:
        return False, f"Error importing CSV: {str(e)}"
    
    return True, phone_numbers

def import_from_txt(file_path):
    """Import phone numbers from text file (one per line)"""
    phone_numbers = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                phone = line.strip()
                if phone:
                    valid, clean_phone = validate_phone(phone)
                    if valid:
                        phone_numbers.append(clean_phone)
    
    except Exception as e:
        return False, f"Error importing TXT: {str(e)}"
    
    return True, phone_numbers

def import_from_json(file_path, key_name='phone'):
    """Import phone numbers from JSON file"""
    phone_numbers = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
            
            # Handle different JSON structures
            if isinstance(data, list):
                # List of objects
                for item in data:
                    if isinstance(item, dict) and key_name in item:
                        phone = str(item[key_name]).strip()
                        valid, clean_phone = validate_phone(phone)
                        if valid:
                            phone_numbers.append(clean_phone)
                    elif isinstance(item, str):
                        # List of strings
                        valid, clean_phone = validate_phone(item)
                        if valid:
                            phone_numbers.append(clean_phone)
            
            elif isinstance(data, dict):
                # Dictionary with phone numbers
                if key_name in data and isinstance(data[key_name], list):
                    for phone in data[key_name]:
                        valid, clean_phone = validate_phone(str(phone))
                        if valid:
                            phone_numbers.append(clean_phone)
    
    except Exception as e:
        return False, f"Error importing JSON: {str(e)}"
    
    return True, phone_numbers

def save_phone_list(phone_numbers, output_file):
    """Save phone numbers to a file"""
    try:
        with open(output_file, 'w', encoding='utf-8') as file:
            for phone in phone_numbers:
                file.write(f"{phone}\n")
        return True, f"Saved {len(phone_numbers)} phone numbers to {output_file}"
    except Exception as e:
        return False, f"Error saving phone list: {str(e)}"