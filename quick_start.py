#!/usr/bin/env python3
"""
Quick Start - Launch both panels immediately
"""

import subprocess
import sys
import time

def main():
    print("🔵 BLACKHAT CONTROL SYSTEM - QUICK START")
    print("=" * 50)
    
    try:
        # Launch offline panel
        print("🖥️ Starting offline control panel...")
        offline_process = subprocess.Popen([sys.executable, 'offline_control_panel.py'])
        print(f"✅ Offline panel started (PID: {offline_process.pid})")
        
        # Wait a moment
        time.sleep(2)
        
        # Launch web server
        print("🌐 Starting web server...")
        web_process = subprocess.Popen([sys.executable, 'Source_Code/http_server.py'])
        print(f"✅ Web server started (PID: {web_process.pid})")
        
        print("\n🎯 BOTH SYSTEMS LAUNCHED!")
        print("=" * 50)
        print("✅ Offline GUI: Should appear as separate window")
        print("✅ Web interface: Will open browser automatically")
        print("🔄 Ports change every 30 seconds for stealth")
        print("\nPress Ctrl+C to stop all processes")
        
        # Keep script running
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping all processes...")
            try:
                offline_process.terminate()
                web_process.terminate()
            except:
                pass
            print("✅ All processes stopped")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n🔧 MANUAL LAUNCH:")
        print("1. Run: python offline_control_panel.py")
        print("2. Run: python Source_Code/http_server.py")

if __name__ == "__main__":
    main()
