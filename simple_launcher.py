#!/usr/bin/env python3
"""
Simple Launcher - Just works!
"""

import tkinter as tk
import subprocess
import sys
import os

def main():
    root = tk.Tk()
    root.title("🔵 BLACKHAT CONTROL SYSTEM")
    root.geometry("500x400")
    root.configure(bg='#000000')
    
    # Make sure window appears
    root.lift()
    root.attributes('-topmost', True)
    root.after_idle(root.attributes, '-topmost', False)
    
    # Title
    title = tk.Label(
        root,
        text="⚠️ BLACKHAT CONTROL SYSTEM ⚠️",
        font=("Arial", 20, "bold"),
        fg="#4a9eff",
        bg="#000000"
    )
    title.pack(pady=30)
    
    subtitle = tk.Label(
        root,
        text="⚡ DUAL INTERFACE LAUNCHER ⚡",
        font=("Arial", 14),
        fg="#7bb3ff",
        bg="#000000"
    )
    subtitle.pack(pady=10)
    
    # Status
    status = tk.Label(
        root,
        text="Choose your interface:",
        font=("Arial", 12),
        fg="#7bb3ff",
        bg="#000000"
    )
    status.pack(pady=20)
    
    # Launch functions
    def launch_offline():
        try:
            subprocess.Popen([sys.executable, 'offline_control_panel.py'])
            status.config(text="✅ Offline GUI panel launched!", fg="#00ff88")
        except Exception as e:
            status.config(text=f"❌ Error: {e}", fg="#ff6666")
    
    def launch_web():
        try:
            # Change to Source_Code directory and launch
            original_dir = os.getcwd()
            os.chdir('Source_Code')
            subprocess.Popen([sys.executable, 'http_server.py'])
            os.chdir(original_dir)
            status.config(text="✅ Web interface launched!", fg="#00ff88")
        except Exception as e:
            status.config(text=f"❌ Error: {e}", fg="#ff6666")
    
    def launch_both():
        try:
            # Launch offline first
            subprocess.Popen([sys.executable, 'offline_control_panel.py'])
            
            # Launch web after short delay
            root.after(2000, lambda: subprocess.Popen([sys.executable, 'Source_Code/http_server.py']))
            
            status.config(text="✅ Both interfaces launching!", fg="#00ff88")
        except Exception as e:
            status.config(text=f"❌ Error: {e}", fg="#ff6666")
    
    # Buttons
    button_frame = tk.Frame(root, bg="#000000")
    button_frame.pack(pady=30)
    
    both_btn = tk.Button(
        button_frame,
        text="🚀 LAUNCH BOTH",
        command=launch_both,
        font=("Arial", 14, "bold"),
        bg="#4a9eff",
        fg="#000000",
        padx=30,
        pady=15
    )
    both_btn.pack(pady=10)
    
    offline_btn = tk.Button(
        button_frame,
        text="🖥️ OFFLINE GUI ONLY",
        command=launch_offline,
        font=("Arial", 12, "bold"),
        bg="#00ff88",
        fg="#000000",
        padx=25,
        pady=12
    )
    offline_btn.pack(pady=5)
    
    web_btn = tk.Button(
        button_frame,
        text="🌐 WEB INTERFACE ONLY",
        command=launch_web,
        font=("Arial", 12, "bold"),
        bg="#ff6600",
        fg="#000000",
        padx=25,
        pady=12
    )
    web_btn.pack(pady=5)
    
    # Instructions
    instructions = tk.Label(
        root,
        text="🚀 BOTH: Offline GUI + Web browser\n🖥️ OFFLINE: Just the GUI panel\n🌐 WEB: Just the browser interface\n\n• Dynamic port rotation every 30 seconds\n• Maximum stealth and anonymity",
        font=("Arial", 10),
        fg="#666666",
        bg="#000000",
        justify="center"
    )
    instructions.pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    main()
