#!/usr/bin/env python3
"""
Simple test server to verify connection
"""

import http.server
import socketserver
import random
import subprocess
import tkinter as tk
from threading import Thread

def find_free_port():
    return random.randint(8000, 8999)

PORT = find_free_port()

class SimpleHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Connection Test</title>
                <style>
                    body {{ background: #000; color: #4a9eff; font-family: monospace; padding: 20px; }}
                    h1 {{ color: #00ff88; }}
                </style>
            </head>
            <body>
                <h1>✅ CONNECTION SUCCESSFUL!</h1>
                <p>🌐 Server running on port: <strong>{PORT}</strong></p>
                <p>🔄 Dynamic port system working!</p>
                <p>⚡ Ready for blackhat operations!</p>
            </body>
            </html>
            """
            
            self.wfile.write(html.encode())
        else:
            super().do_GET()

def start_server():
    global PORT
    try:
        with socketserver.TCPServer(("", PORT), SimpleHandler) as httpd:
            httpd.allow_reuse_address = True
            print(f"✅ Test server running on port {PORT}")
            print(f"🌐 Access: http://localhost:{PORT}")
            
            # Auto-open browser
            subprocess.run(['start', f'http://localhost:{PORT}'], shell=True)
            
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ Error: {e}")

def create_test_gui():
    root = tk.Tk()
    root.title("Connection Test")
    root.geometry("400x250")
    root.configure(bg='#000000')

    title = tk.Label(root, text="🔧 CONNECTION TEST", font=("Arial", 16, "bold"), fg="#4a9eff", bg="#000000")
    title.pack(pady=20)

    port_label = tk.Label(root, text=f"🌐 Testing Port: {PORT}", font=("Arial", 12), fg="#00ff88", bg="#000000")
    port_label.pack(pady=10)

    status_label = tk.Label(root, text="Starting server...", font=("Arial", 10), fg="#7bb3ff", bg="#000000")
    status_label.pack(pady=10)

    start_btn = tk.Button(
        root,
        text="🚀 START TEST SERVER",
        command=lambda: Thread(target=start_server, daemon=True).start(),
        font=("Arial", 12, "bold"),
        bg="#4a9eff",
        fg="#000000"
    )
    start_btn.pack(pady=10)

    auto_start_btn = tk.Button(
        root,
        text="⚡ AUTO START NOW",
        command=lambda: [
            Thread(target=start_server, daemon=True).start(),
            status_label.config(text=f"Server running on port {PORT}")
        ],
        font=("Arial", 12, "bold"),
        bg="#00ff88",
        fg="#000000"
    )
    auto_start_btn.pack(pady=5)

    # Auto-start after 2 seconds
    def auto_start():
        Thread(target=start_server, daemon=True).start()
        status_label.config(text=f"✅ Auto-started on port {PORT}")
        auto_start_btn.config(text="✅ RUNNING", state="disabled")

    root.after(2000, auto_start)  # Auto-start after 2 seconds

    root.mainloop()

if __name__ == "__main__":
    create_test_gui()
