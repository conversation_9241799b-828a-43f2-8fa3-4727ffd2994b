"""
Mass SMS Integration Module
"""
import requests
import json
import time
import random
import threading
try:
    from Source_Code.config import TOR_PROXY, VPN_PROXY
    SMS_API_KEYS = {}  # Define locally if not in config
except ImportError:
    # Fallback configuration
    TOR_PROXY = {'http': 'socks5://127.0.0.1:9050', 'https': 'socks5://127.0.0.1:9050'}
    VPN_PROXY = {'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'}
    SMS_API_KEYS = {}

class SmsManager:
    def __init__(self, use_tor=False, use_vpn=False):
        """Initialize SMS manager with anonymity options"""
        self.proxies = {}
        if use_tor:
            self.proxies.update(TOR_PROXY)
        if use_vpn:
            self.proxies.update(VPN_PROXY)
        
        self.api_keys = SMS_API_KEYS
        self.services = ["textbelt", "sms77", "twilio", "nexmo", "sinch"]
        self.current_service = 0
        self.rate_limit_wait = 60  # seconds to wait after rate limit
    
    def rotate_service(self):
        """Rotate to next SMS service"""
        self.current_service = (self.current_service + 1) % len(self.services)
        return self.services[self.current_service]
    
    def send_single_sms(self, phone, message, service=None):
        """Send SMS to a single number using specified service"""
        if not service:
            service = self.services[self.current_service]
            
        if service == "textbelt":
            return self._send_textbelt(phone, message)
        elif service == "sms77":
            return self._send_sms77(phone, message)
        elif service == "twilio":
            return self._send_twilio(phone, message)
        elif service == "nexmo":
            return self._send_nexmo(phone, message)
        elif service == "sinch":
            return self._send_sinch(phone, message)
        else:
            return False, "Unknown service"
    
    def send_mass_sms(self, phone_list, message, delay_min=1, delay_max=5):
        """Send SMS to multiple numbers with random delays"""
        results = []
        success_count = 0
        
        for phone in phone_list:
            # Random delay between messages
            if len(results) > 0:
                delay = random.uniform(delay_min, delay_max)
                time.sleep(delay)
            
            # Try sending with current service
            success, response = self.send_single_sms(phone, message)
            
            # If failed, try with next service
            if not success:
                self.rotate_service()
                success, response = self.send_single_sms(phone, message)
            
            results.append({
                "phone": phone,
                "success": success,
                "response": response
            })
            
            if success:
                success_count += 1
        
        return {
            "total": len(phone_list),
            "success": success_count,
            "failed": len(phone_list) - success_count,
            "details": results
        }
    
    def send_mass_sms_async(self, phone_list, message, callback=None):
        """Send SMS to multiple numbers asynchronously"""
        thread = threading.Thread(
            target=self._async_mass_sms,
            args=(phone_list, message, callback)
        )
        thread.daemon = True
        thread.start()
        return True, "Mass SMS sending started in background"
    
    def _async_mass_sms(self, phone_list, message, callback):
        """Background thread for sending mass SMS"""
        result = self.send_mass_sms(phone_list, message)
        if callback:
            callback(result)
    
    def _send_textbelt(self, phone, message):
        """Send SMS using TextBelt API"""
        try:
            api_key = self.api_keys.get("textbelt", "")
            response = requests.post(
                'https://textbelt.com/text',
                {
                    'phone': phone,
                    'message': message,
                    'key': api_key,
                },
                proxies=self.proxies,
                timeout=10
            )
            data = response.json()
            return data.get('success', False), data
        except Exception as e:
            return False, str(e)
    
    def _send_sms77(self, phone, message):
        """Send SMS using SMS77 API"""
        try:
            api_key = self.api_keys.get("sms77", "")
            response = requests.post(
                'https://gateway.sms77.io/api/sms',
                data={
                    'to': phone,
                    'text': message,
                    'from': 'Update',
                    'json': 1
                },
                headers={
                    'X-Api-Key': api_key
                },
                proxies=self.proxies,
                timeout=10
            )
            data = response.json()
            return data.get('success', False), data
        except Exception as e:
            return False, str(e)
    
    def _send_twilio(self, phone, message):
        """Send SMS using Twilio API"""
        # Implementation for Twilio
        return False, "Not implemented"
    
    def _send_nexmo(self, phone, message):
        """Send SMS using Nexmo/Vonage API"""
        # Implementation for Nexmo
        return False, "Not implemented"
    
    def _send_sinch(self, phone, message):
        """Send SMS using Sinch API"""
        # Implementation for Sinch
        return False, "Not implemented"