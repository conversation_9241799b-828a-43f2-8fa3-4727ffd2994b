"""
SMS Panel Integration Module
Connects the GUI with the custom SMS panel
"""
import requests
import json
import os
import time
import threading
from datetime import datetime

class SmsPanelIntegration:
    def __init__(self, host="localhost", port=5000, username="admin", password="admin123"):
        """Initialize SMS Panel integration"""
        self.base_url = f"http://{host}:{port}"
        self.username = username
        self.password = password
        self.api_key = None
        self.authenticated = False
        self.session = requests.Session()
    
    def authenticate(self):
        """Authenticate with the SMS panel"""
        try:
            # First try to authenticate with API key if available
            if self.api_key:
                response = self.session.get(
                    f"{self.base_url}/api/status",
                    headers={"X-API-Key": self.api_key}
                )
                if response.status_code == 200:
                    self.authenticated = True
                    return True, "Authenticated with API key"
            
            # If API key authentication fails, try username/password
            response = self.session.post(
                f"{self.base_url}/login",
                data={"username": self.username, "password": self.password}
            )
            
            if response.url.endswith('/'):  # Successful login redirects to root
                self.authenticated = True
                return True, "Authenticated with username/password"
            else:
                return False, "Authentication failed"
        except Exception as e:
            return False, f"Connection error: {str(e)}"
    
    def get_api_key(self):
        """Get or create API key for the user"""
        if not self.authenticated:
            success, message = self.authenticate()
            if not success:
                return False, message
        
        try:
            # Get API keys page
            response = self.session.get(f"{self.base_url}/admin/api-keys")
            
            # Check if we have any keys
            # In a real implementation, we would parse the HTML to find existing keys
            # For simplicity, we'll create a new key
            
            # Create new API key
            response = self.session.post(
                f"{self.base_url}/admin/api-keys",
                data={"action": "add", "name": "GUI Integration", "owner": self.username}
            )
            
            # Get the API key from the response
            # In a real implementation, we would parse the HTML to find the key
            # For simplicity, we'll use a placeholder
            self.api_key = "api_key_placeholder"
            
            return True, self.api_key
        except Exception as e:
            return False, f"Error getting API key: {str(e)}"
    
    def send_sms(self, phone, message, sender="INFO"):
        """Send a single SMS through the panel"""
        if not self.api_key:
            success, result = self.get_api_key()
            if not success:
                return False, result
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/send",
                headers={"X-API-Key": self.api_key},
                json={"phone": phone, "message": message, "sender": sender}
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("success", False), data
            else:
                return False, f"API error: {response.status_code}"
        except Exception as e:
            return False, f"Error sending SMS: {str(e)}"
    
    def send_bulk_sms(self, phones, message, sender="INFO"):
        """Send bulk SMS through the panel"""
        if not self.api_key:
            success, result = self.get_api_key()
            if not success:
                return False, result
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/bulk-send",
                headers={"X-API-Key": self.api_key},
                json={"phones": phones, "message": message, "sender": sender}
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("success", False), data
            else:
                return False, f"API error: {response.status_code}"
        except Exception as e:
            return False, f"Error sending bulk SMS: {str(e)}"
    
    def get_status(self):
        """Get SMS panel status"""
        if not self.api_key:
            success, result = self.get_api_key()
            if not success:
                return False, result
        
        try:
            response = self.session.get(
                f"{self.base_url}/api/status",
                headers={"X-API-Key": self.api_key}
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("success", False), data
            else:
                return False, f"API error: {response.status_code}"
        except Exception as e:
            return False, f"Error getting status: {str(e)}"
    
    def start_panel(self):
        """Start the SMS panel if not already running"""
        # Check if panel is already running
        try:
            response = requests.get(f"{self.base_url}/login", timeout=2)
            return True, "SMS panel is already running"
        except:
            pass
        
        # Start panel in a separate process
        try:
            import subprocess
            import sys
            
            # Get the path to the custom_sms_panel.py file
            panel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "custom_sms_panel.py")
            
            # Start the panel
            subprocess.Popen([sys.executable, panel_path], 
                            stdout=subprocess.PIPE, 
                            stderr=subprocess.PIPE)
            
            # Wait for panel to start
            time.sleep(2)
            
            # Check if panel is running
            try:
                response = requests.get(f"{self.base_url}/login", timeout=2)
                return True, "SMS panel started successfully"
            except:
                return False, "Failed to start SMS panel"
        except Exception as e:
            return False, f"Error starting SMS panel: {str(e)}"