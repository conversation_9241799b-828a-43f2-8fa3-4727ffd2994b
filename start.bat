@echo off
echo 🔵 BLACKHAT CONTROL SYSTEM
echo ⚡ Professional Stealth Interface
echo =======================================

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found. Please install Python 3.7 or higher.
    echo    Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Check Python version
for /f "tokens=2" %%a in ('python --version 2^>^&1') do set PYVER=%%a
echo ✓ Python %PYVER% detected

REM Create necessary directories
echo Creating directories...
mkdir logs 2>nul
mkdir data 2>nul
mkdir configs 2>nul
mkdir apk 2>nul
mkdir apk_configs 2>nul
mkdir reports 2>nul

REM Install required packages
echo Installing required packages...
pip install requests cryptography flask flask-httpauth PyQt5 waitress gunicorn

REM Check for Tor proxy
curl --socks5 127.0.0.1:9050 https://check.torproject.org/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Tor proxy detected
) else (
    echo ⚠️ Tor proxy not detected. Some features may be limited.
)

REM Check for VPN proxy
curl --proxy 127.0.0.1:8080 https://api.ipify.org/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ VPN proxy detected
) else (
    echo ⚠️ VPN proxy not detected. Some features may be limited.
)

REM Start the SMS Panel Connector
echo Starting SMS Panel Connector...
start "SMS Panel" cmd /c "python sms_panel_connector.py"
echo ✓ SMS Panel started on http://localhost:5000

REM Start the main system
echo Starting Blackhat Control System...
python Source_Code\start.py

echo ✓ System started!
pause




