#!/usr/bin/env python3
"""
Direct launcher for both panels - Guaranteed to work
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time
import os

def main():
    print("Starting dual control system...")
    
    # Create a simple status window
    root = tk.Tk()
    root.title("Dual Control System Launcher")
    root.geometry("400x300")
    root.configure(bg='#000000')
    
    title = tk.Label(root, text="🔵 DUAL CONTROL SYSTEM", font=("Arial", 16, "bold"), fg="#4a9eff", bg="#000000")
    title.pack(pady=20)
    
    status = tk.Label(root, text="Starting both interfaces...", font=("Arial", 12), fg="#7bb3ff", bg="#000000")
    status.pack(pady=10)
    
    def start_offline():
        try:
            status.config(text="✅ Starting offline panel...")
            root.update()
            
            # Import and run offline panel directly
            import sys
            sys.path.append('.')
            
            # Import the offline control panel class
            from offline_control_panel import BlackhatControlPanel
            
            # Create and run the offline panel
            app = BlackhatControlPanel()
            app.run()
            
        except Exception as e:
            print(f"Offline panel error: {e}")
            status.config(text=f"❌ Offline panel failed: {e}")
    
    def start_web():
        try:
            time.sleep(3)  # Wait for offline panel to start
            status.config(text="✅ Offline panel started, starting web server...")
            root.update()
            
            # Change to Source_Code directory
            original_dir = os.getcwd()
            os.chdir('Source_Code')
            
            # Import and run web server
            import sys
            sys.path.append('.')
            
            # Import the web server components
            import http_server
            
            # Run the GUI
            http_server.create_gui()
            
        except Exception as e:
            print(f"Web server error: {e}")
            status.config(text=f"❌ Web server failed: {e}")
        finally:
            try:
                os.chdir(original_dir)
            except:
                pass
    
    # Start offline panel immediately
    threading.Thread(target=start_offline, daemon=True).start()
    
    # Start web server after delay
    threading.Thread(target=start_web, daemon=True).start()
    
    # Update status
    def update_status():
        status.config(text="✅ Both systems should be starting...")
        root.after(5000, lambda: status.config(text="✅ Check for both GUI windows"))
    
    root.after(1000, update_status)
    
    # Add manual launch buttons as backup
    button_frame = tk.Frame(root, bg="#000000")
    button_frame.pack(pady=20)
    
    def manual_offline():
        try:
            import subprocess
            subprocess.Popen(['python', 'offline_control_panel.py'])
            status.config(text="✅ Manual offline panel launched")
        except Exception as e:
            status.config(text=f"❌ Manual offline failed: {e}")
    
    def manual_web():
        try:
            import subprocess
            subprocess.Popen(['python', 'Source_Code/http_server.py'])
            status.config(text="✅ Manual web server launched")
        except Exception as e:
            status.config(text=f"❌ Manual web failed: {e}")
    
    offline_btn = tk.Button(button_frame, text="🖥️ MANUAL OFFLINE", command=manual_offline, 
                           font=("Arial", 10, "bold"), bg="#00ff88", fg="#000000")
    offline_btn.pack(side='left', padx=10)
    
    web_btn = tk.Button(button_frame, text="🌐 MANUAL WEB", command=manual_web,
                       font=("Arial", 10, "bold"), bg="#ff6600", fg="#000000")
    web_btn.pack(side='left', padx=10)
    
    instructions = tk.Label(root, text="If automatic start fails, use manual buttons above", 
                           font=("Arial", 9), fg="#666666", bg="#000000")
    instructions.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    main()
