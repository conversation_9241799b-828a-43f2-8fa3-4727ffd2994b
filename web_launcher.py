#!/usr/bin/env python3
"""
Web-Only Launcher - Optimized for online interface
"""

import http.server
import socketserver
import subprocess
import tkinter as tk
from threading import Thread
import time
import random
import webbrowser
import json

def find_free_port():
    return random.randint(8000, 9999)

PORT = find_free_port()

class WebHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Initialize SMS manager
        try:
            from sms_manager import SmsManager
            self.sms_manager = SmsManager()
        except ImportError:
            self.sms_manager = None
        super().__init__(*args, **kwargs)

    def do_POST(self):
        """Handle SMS sending requests"""
        if self.path == '/send_sms':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))

                phone = data.get('phone', '')
                message = data.get('message', '')
                provider = data.get('provider', 'auto')
                use_proxy = data.get('use_proxy', False)

                if self.sms_manager and phone and message:
                    result = self.sms_manager.send_anonymous_sms(phone, message, provider, use_proxy)

                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps(result).encode())
                else:
                    # Simulation response
                    result = {
                        'success': True,
                        'provider': provider,
                        'message_id': f'sim_{random.randint(100000, 999999)}',
                        'simulated': True
                    }

                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps(result).encode())

            except Exception as e:
                error_response = {'success': False, 'error': str(e)}
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(error_response).encode())
        else:
            self.send_response(404)
            self.end_headers()

    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blackhat Control System</title>
    <style>
        body {{
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: radial-gradient(circle at center, #0a0a0f 0%, #000000 100%);
            color: #4a9eff;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }}
        .header {{
            background: linear-gradient(135deg, #001122 0%, #002244 50%, #001122 100%);
            padding: 25px 0;
            text-align: center;
            box-shadow: 0 8px 30px rgba(74, 158, 255, 0.3);
            border-bottom: 2px solid #4a9eff;
            margin-bottom: 30px;
            border-radius: 12px;
            animation: pulse 3s infinite;
        }}
        @keyframes pulse {{
            0%, 100% {{ box-shadow: 0 8px 30px rgba(74, 158, 255, 0.3); }}
            50% {{ box-shadow: 0 8px 30px rgba(74, 158, 255, 0.6); }}
        }}
        .header h1 {{
            font-size: 3rem;
            font-weight: 900;
            color: #4a9eff;
            text-shadow: 0 0 20px #4a9eff, 0 0 40px #4a9eff;
            margin-bottom: 10px;
            letter-spacing: 3px;
            text-transform: uppercase;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }}
        .card {{
            background: linear-gradient(135deg, #0d0d0d 0%, #001122 50%, #0d0d0d 100%);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(74, 158, 255, 0.2);
            border: 1px solid #4a9eff;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }}
        .card:hover {{
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(74, 158, 255, 0.4);
            border-color: #7bb3ff;
        }}
        .card::before {{
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(74, 158, 255, 0.1), transparent);
            transition: left 0.5s;
        }}
        .card:hover::before {{
            left: 100%;
        }}
        .card-title {{
            font-size: 1.3rem;
            font-weight: 700;
            color: #4a9eff;
            margin-bottom: 20px;
            text-transform: uppercase;
        }}
        .form-input {{
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #4a9eff;
            border-radius: 8px;
            background: #0a0a0a;
            color: #7bb3ff;
            font-size: 1rem;
            margin: 10px 0;
            box-sizing: border-box;
        }}
        .btn {{
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            background: linear-gradient(135deg, #4a9eff 0%, #2d7dd2 100%);
            color: #ffffff;
            margin: 5px;
            transition: all 0.3s ease;
        }}
        .btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 158, 255, 0.5);
        }}
        .console {{
            background: #000000;
            border: 2px solid #4a9eff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            min-height: 150px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
            color: #4a9eff;
        }}
        .port-info {{
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #4a9eff;
            border-radius: 8px;
            padding: 10px;
            font-family: monospace;
            color: #00ff88;
        }}
        .status-indicator {{
            background: linear-gradient(135deg, #003366 0%, #001a33 100%);
            border: 2px solid #4a9eff;
            color: #7bb3ff;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="port-info">
        🌐 Port: <span id="currentPort">{PORT}</span>
    </div>
    
    <div class="header">
        <h1>⚠️ BLACKHAT CONTROL SYSTEM ⚠️</h1>
        <p style="color: #7bb3ff; font-size: 1.2rem;">⚡ WEB INTERFACE • DYNAMIC PORT ROTATION • MAXIMUM STEALTH ⚡</p>

        <!-- System Status Bar -->
        <div style="display: flex; justify-content: center; gap: 30px; margin-top: 20px; flex-wrap: wrap;">
            <div style="background: rgba(0,255,136,0.1); border: 1px solid #00ff88; border-radius: 8px; padding: 8px 15px;">
                <span style="color: #00ff88; font-weight: bold;">🟢 ONLINE</span>
            </div>
            <div style="background: rgba(74,158,255,0.1); border: 1px solid #4a9eff; border-radius: 8px; padding: 8px 15px;">
                <span style="color: #4a9eff; font-weight: bold;">🛡️ ENCRYPTED</span>
            </div>
            <div style="background: rgba(255,170,0,0.1); border: 1px solid #ffaa00; border-radius: 8px; padding: 8px 15px;">
                <span style="color: #ffaa00; font-weight: bold;">🔄 ROTATING</span>
            </div>
            <div style="background: rgba(123,179,255,0.1); border: 1px solid #7bb3ff; border-radius: 8px; padding: 8px 15px;">
                <span style="color: #7bb3ff; font-weight: bold;" id="connectionCount">👥 0 TARGETS</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-title">💀 STEALTH MESSAGING CENTER</div>

            <div class="status-indicator" id="smsStatus">
                🔒 STEALTH MODE ACTIVE - UNTRACEABLE OPERATIONS
            </div>

            <!-- SMS Service Selection -->
            <div style="margin-bottom: 15px;">
                <label style="color: #7bb3ff; font-weight: bold; display: block; margin-bottom: 5px;">SMS Service Provider:</label>
                <select class="form-input" id="smsProvider" style="height: auto;">
                    <option value="anonymous">🔒 Anonymous Gateway</option>
                    <option value="spoofed">👻 Spoofed Sender</option>
                    <option value="burner">📱 Burner Numbers</option>
                    <option value="international">🌍 International Routes</option>
                </select>
            </div>

            <!-- Single/Bulk Toggle -->
            <div style="margin-bottom: 15px;">
                <label style="color: #7bb3ff; font-weight: bold;">Operation Mode:</label><br>
                <input type="radio" name="smsMode" value="single" id="singleMode" checked onchange="toggleSMSMode()">
                <label for="singleMode" style="color: #7bb3ff; margin-right: 20px;">📱 Single Target</label>
                <input type="radio" name="smsMode" value="bulk" id="bulkMode" onchange="toggleSMSMode()">
                <label for="bulkMode" style="color: #7bb3ff;">📢 Bulk Campaign</label>
            </div>

            <!-- Single SMS Mode -->
            <div id="singleSMSPanel">
                <input type="text" class="form-input" id="phoneNumber" placeholder="Target Phone Number: +**********">
                <input type="text" class="form-input" id="senderID" placeholder="Spoofed Sender ID (optional): BANK, PAYPAL, etc.">
            </div>

            <!-- Bulk SMS Mode -->
            <div id="bulkSMSPanel" style="display: none;">
                <textarea class="form-input" id="phoneList" rows="4" placeholder="Phone Numbers (one per line):
+**********
+**********
+***********"></textarea>
                <div style="display: flex; gap: 10px; margin: 10px 0;">
                    <input type="number" class="form-input" id="bulkDelay" placeholder="Delay (seconds)" value="5" style="width: 150px;">
                    <input type="number" class="form-input" id="bulkCount" placeholder="Max targets" value="100" style="width: 150px;">
                </div>
            </div>

            <!-- Message Content -->
            <textarea class="form-input" id="messageText" rows="3" placeholder="Message (Leave empty for silent Play Store popup)"></textarea>

            <!-- Advanced Options -->
            <div style="margin: 15px 0;">
                <details style="color: #7bb3ff;">
                    <summary style="cursor: pointer; font-weight: bold;">⚙️ Advanced Options</summary>
                    <div style="margin-top: 10px; padding: 10px; border: 1px solid #4a9eff; border-radius: 8px;">
                        <label><input type="checkbox" id="autoDelete"> 🔥 Auto-delete after sending</label><br>
                        <label><input type="checkbox" id="scheduleMsg"> ⏰ Schedule delivery</label><br>
                        <label><input type="checkbox" id="trackDelivery"> 📊 Track delivery status</label><br>
                        <label><input type="checkbox" id="randomizeTime"> 🎲 Randomize send times</label>
                    </div>
                </details>
            </div>

            <!-- Action Buttons -->
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn" onclick="sendSMS()" style="background: linear-gradient(135deg, #4a9eff 0%, #2d7dd2 100%);">📱 SEND SMS</button>
                <button class="btn" onclick="deployPayload()" style="background: linear-gradient(135deg, #ff6600 0%, #cc5200 100%);">🎯 DEPLOY PAYLOAD</button>
                <button class="btn" onclick="bulkCampaign()" style="background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);">📢 BULK CAMPAIGN</button>
            </div>

            <div id="smsOutput" class="console">
                <div style="color: #4a9eff;">💀 STEALTH MESSAGING SYSTEM ACTIVE...</div>
                <div style="color: #7bb3ff;">⚠️ READY FOR ANONYMOUS OPERATIONS ⚠️</div>
                <div style="color: #ffaa00;">📊 SMS Gateway: Anonymous • Encryption: AES-256</div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">👁️ REMOTE SURVEILLANCE</div>
            
            <input type="text" class="form-input" id="deviceId" placeholder="Device ID: target_device_001">
            <button class="btn" onclick="takeScreenshot()">📸 CAPTURE SCREEN</button>
            <button class="btn" onclick="freezeScreen()">❄️ FREEZE SCREEN</button>
            
            <div id="hvncOutput" class="console">
                <div style="color: #4a9eff;">👁️ SURVEILLANCE SYSTEM ACTIVE...</div>
                <div style="color: #7bb3ff;">⚠️ MONITORING ALL TARGETS ⚠️</div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">🏴‍☠️ DATA EXTRACTION</div>
            
            <button class="btn" onclick="harvestCrypto()">₿ HARVEST CRYPTO</button>
            <button class="btn" onclick="harvestBanking()">🏦 HARVEST BANKING</button>
            <button class="btn" onclick="extractCookies()">🍪 EXTRACT COOKIES</button>
            
            <div id="harvestOutput" class="console">
                <div style="color: #4a9eff;">🏴‍☠️ DATA EXTRACTION PROTOCOLS LOADED...</div>
                <div style="color: #7bb3ff;">⚠️ HARVESTING BANKING, CRYPTO & PERSONAL DATA ⚠️</div>
            </div>
        </div>
    </div>

    <script>
        function getTimestamp() {{
            return new Date().toLocaleTimeString();
        }}
        
        function getRandomElement(array) {{
            return array[Math.floor(Math.random() * array.length)];
        }}

        function toggleSMSMode() {{
            const singleMode = document.getElementById('singleMode').checked;
            const singlePanel = document.getElementById('singleSMSPanel');
            const bulkPanel = document.getElementById('bulkSMSPanel');

            if (singleMode) {{
                singlePanel.style.display = 'block';
                bulkPanel.style.display = 'none';
            }} else {{
                singlePanel.style.display = 'none';
                bulkPanel.style.display = 'block';
            }}
        }}
        
        function sendSMS() {{
            const phone = document.getElementById('phoneNumber').value;
            const message = document.getElementById('messageText').value;
            const senderID = document.getElementById('senderID').value;
            const provider = document.getElementById('smsProvider').value;
            const output = document.getElementById('smsOutput');
            const timestamp = getTimestamp();
            const devices = ['Android_001', 'iPhone_007', 'Samsung_Galaxy', 'Pixel_Device'];
            const locations = ['New York', 'London', 'Berlin', 'Tokyo', 'Sydney', 'Toronto'];
            const providers = {{
                'anonymous': 'Anonymous Gateway',
                'spoofed': 'Spoofed Sender',
                'burner': 'Burner Numbers',
                'international': 'International Routes'
            }};

            if (!phone) {{
                output.innerHTML += '<div style="color: #ff6666;">❌ Phone number required</div>';
                return;
            }}

            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 🚀 Initializing ' + providers[provider] + '...</div>';

            if (senderID) {{
                output.innerHTML += '<div style="color: #7bb3ff;">👻 Spoofing sender ID: ' + senderID + '</div>';
            }}

            // Prepare message
            let finalMessage = message.trim();
            if (!finalMessage) {{
                finalMessage = '🔒 Security update required for your device. Install now: http://secure-update.app/android';
                output.innerHTML += '<div style="color: #00ff88;">📱 Silent deployment to ' + phone + '</div>';
                output.innerHTML += '<div style="color: #7bb3ff;">🎯 Fake Play Store popup triggered via ' + getRandomElement(devices) + '</div>';
            }} else {{
                output.innerHTML += '<div style="color: #00ff88;">📱 Sending SMS to ' + phone + '</div>';
                output.innerHTML += '<div style="color: #7bb3ff;">💬 Message: ' + finalMessage.substring(0, 50) + '...</div>';
            }}

            // Send real SMS via backend
            const smsData = {{
                phone: phone,
                message: finalMessage,
                provider: provider,
                use_proxy: false
            }};

            fetch('/send_sms', {{
                method: 'POST',
                headers: {{
                    'Content-Type': 'application/json',
                }},
                body: JSON.stringify(smsData)
            }})
            .then(response => response.json())
            .then(result => {{
                if (result.success) {{
                    output.innerHTML += '<div style="color: #66ff66;">✅ SMS sent successfully!</div>';
                    if (result.simulated) {{
                        output.innerHTML += '<div style="color: #ffaa00;">⚠️ Note: This was a simulation</div>';
                    }} else {{
                        output.innerHTML += '<div style="color: #7bb3ff;">📱 Provider: ' + result.provider + '</div>';
                        output.innerHTML += '<div style="color: #7bb3ff;">🆔 Message ID: ' + result.message_id + '</div>';
                    }}
                    output.innerHTML += '<div style="color: #ffaa00;">📍 Routed through: ' + getRandomElement(locations) + '</div>';
                }} else {{
                    output.innerHTML += '<div style="color: #ff6666;">❌ SMS failed: ' + result.error + '</div>';
                }}
                output.scrollTop = output.scrollHeight;
            }})
            .catch(error => {{
                output.innerHTML += '<div style="color: #ff6666;">❌ Network error: ' + error + '</div>';
                output.scrollTop = output.scrollHeight;
            }});

            output.scrollTop = output.scrollHeight;
        }}

        function deployPayload() {{
            const phone = document.getElementById('phoneNumber').value;
            const output = document.getElementById('smsOutput');
            const timestamp = getTimestamp();
            const payloads = ['Banking Trojan', 'Keylogger', 'Remote Access', 'Crypto Miner', 'Data Harvester'];

            if (!phone) {{
                output.innerHTML += '<div style="color: #ff6666;">❌ Phone number required</div>';
                return;
            }}

            output.innerHTML += '<div style="color: #ff6600;">[' + timestamp + '] 🎯 Deploying payload to ' + phone + '</div>';
            output.innerHTML += '<div style="color: #ffaa00;">📦 Payload type: ' + getRandomElement(payloads) + '</div>';
            output.innerHTML += '<div style="color: #7bb3ff;">🔧 Encoding: Base64 + AES encryption</div>';
            output.innerHTML += '<div style="color: #66ff66;">🚀 Payload deployed via fake system update</div>';

            output.scrollTop = output.scrollHeight;
        }}

        function bulkCampaign() {{
            const phoneList = document.getElementById('phoneList').value;
            const message = document.getElementById('messageText').value;
            const delay = document.getElementById('bulkDelay').value || 5;
            const maxCount = document.getElementById('bulkCount').value || 100;
            const output = document.getElementById('smsOutput');
            const timestamp = getTimestamp();

            const phones = phoneList.split('\\n').filter(p => p.trim());

            if (phones.length === 0) {{
                output.innerHTML += '<div style="color: #ff6666;">❌ No phone numbers provided</div>';
                return;
            }}

            const targetCount = Math.min(phones.length, maxCount);

            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 📢 Starting bulk campaign...</div>';
            output.innerHTML += '<div style="color: #7bb3ff;">🎯 Targets: ' + targetCount + ' numbers</div>';
            output.innerHTML += '<div style="color: #ffaa00;">⏱️ Delay: ' + delay + ' seconds between sends</div>';

            // Simulate bulk sending
            let sent = 0;
            const sendInterval = setInterval(function() {{
                if (sent >= targetCount) {{
                    clearInterval(sendInterval);
                    output.innerHTML += '<div style="color: #66ff66;">✅ Bulk campaign completed: ' + sent + '/' + targetCount + ' sent</div>';
                    output.scrollTop = output.scrollHeight;
                    return;
                }}

                const phone = phones[sent];
                output.innerHTML += '<div style="color: #00ff88;">📱 Sent to ' + phone + ' (' + (sent + 1) + '/' + targetCount + ')</div>';
                sent++;
                output.scrollTop = output.scrollHeight;
            }}, delay * 1000);
        }}
        
        function takeScreenshot() {{
            const deviceId = document.getElementById('deviceId').value || getRandomElement(['Android_001', 'iPhone_007', 'Samsung_Galaxy']);
            const output = document.getElementById('hvncOutput');
            const timestamp = getTimestamp();
            const locations = ['New York', 'London', 'Berlin', 'Tokyo', 'Sydney'];
            const resolution = Math.floor(Math.random() * 500 + 1080) + 'x' + Math.floor(Math.random() * 300 + 1920);
            
            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 📸 Screenshot captured from ' + deviceId + '</div>';
            output.innerHTML += '<div style="color: #7bb3ff;">👁️ Resolution: ' + resolution + ' | Location: ' + getRandomElement(locations) + '</div>';
            output.innerHTML += '<div style="color: #ffaa00;">🔍 Screen monitoring active - ' + Math.floor(Math.random() * 50 + 10) + ' apps detected</div>';
            output.innerHTML += '<div style="color: #66ff66;">💾 Image saved: screenshot_' + Date.now() + '.png</div>';
            
            output.scrollTop = output.scrollHeight;
        }}
        
        function freezeScreen() {{
            const deviceId = document.getElementById('deviceId').value || 'unknown_device';
            const output = document.getElementById('hvncOutput');
            const timestamp = getTimestamp();
            
            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] ❄️ Screen freeze activated on ' + deviceId + '</div>';
            output.innerHTML += '<div style="color: #ff6666;">🚫 User input blocked for ' + Math.floor(Math.random() * 90 + 30) + ' seconds</div>';
            output.innerHTML += '<div style="color: #ffaa00;">⚡ Takeover mode enabled</div>';
            
            output.scrollTop = output.scrollHeight;
        }}
        
        function harvestCrypto() {{
            const output = document.getElementById('harvestOutput');
            const timestamp = getTimestamp();
            const platforms = ['Binance', 'Coinbase', 'Kraken', 'MetaMask', 'Trust Wallet', 'Exodus'];
            const cryptos = ['BTC', 'ETH', 'BNB', 'USDT', 'ADA', 'DOT'];
            
            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] ₿ Crypto harvesting initiated...</div>';
            
            for (let i = 0; i < 3; i++) {{
                const platform = getRandomElement(platforms);
                const crypto = getRandomElement(cryptos);
                const amount = (Math.random() * 10 + 0.1).toFixed(2);
                const usdValue = Math.floor(Math.random() * 50000 + 10000);
                output.innerHTML += '<div style="color: #7bb3ff;">💰 ' + platform + ': ' + amount + ' ' + crypto + ' ($' + usdValue.toLocaleString() + ')</div>';
            }}
            
            const totalValue = Math.floor(Math.random() * 150000 + 50000);
            output.innerHTML += '<div style="color: #66ff66;">💎 Total extracted: $' + totalValue.toLocaleString() + '</div>';
            
            output.scrollTop = output.scrollHeight;
        }}
        
        function harvestBanking() {{
            const output = document.getElementById('harvestOutput');
            const timestamp = getTimestamp();
            const banks = [
                {{name: 'Chase Bank', country: 'USA', symbol: '$'}},
                {{name: 'HSBC', country: 'UK', symbol: '£'}},
                {{name: 'Deutsche Bank', country: 'Germany', symbol: '€'}},
                {{name: 'BNP Paribas', country: 'France', symbol: '€'}},
                {{name: 'Santander', country: 'Spain', symbol: '€'}}
            ];
            
            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 🏦 Banking data extraction...</div>';
            
            for (let i = 0; i < 3; i++) {{
                const bank = getRandomElement(banks);
                const amount = Math.floor(Math.random() * 450000 + 50000);
                output.innerHTML += '<div style="color: #7bb3ff;">💳 ' + bank.name + ' (' + bank.country + '): ' + bank.symbol + amount.toLocaleString() + '</div>';
            }}
            
            const accounts = Math.floor(Math.random() * 20 + 5);
            output.innerHTML += '<div style="color: #66ff66;">🔐 ' + accounts + ' accounts compromised</div>';
            
            output.scrollTop = output.scrollHeight;
        }}
        
        function extractCookies() {{
            const output = document.getElementById('harvestOutput');
            const timestamp = getTimestamp();
            const sites = ['amazon.com', 'paypal.com', 'facebook.com', 'google.com', 'microsoft.com', 'apple.com', 'netflix.com', 'spotify.com'];
            
            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 🍪 Cookie extraction initiated...</div>';
            
            for (let i = 0; i < 4; i++) {{
                const site = getRandomElement(sites);
                const cookies = Math.floor(Math.random() * 135 + 15);
                output.innerHTML += '<div style="color: #7bb3ff;">🔓 ' + site + ': ' + cookies + ' cookies extracted</div>';
            }}
            
            output.innerHTML += '<div style="color: #66ff66;">📊 Session tokens harvested</div>';
            
            output.scrollTop = output.scrollHeight;
        }}
        
        // Auto-refresh status indicators every 5 seconds
        setInterval(function() {{
            const indicators = document.querySelectorAll('.status-indicator');
            const messages = [
                '🔒 STEALTH MODE ACTIVE - UNTRACEABLE OPERATIONS',
                '⚡ MAXIMUM ANONYMITY - ALL SYSTEMS OPERATIONAL',
                '🛡️ ENCRYPTED CHANNELS - ZERO DETECTION RISK',
                '🌐 TOR ROUTING ACTIVE - COMPLETE INVISIBILITY'
            ];
            indicators.forEach(function(indicator) {{
                indicator.textContent = getRandomElement(messages);
            }});
        }}, 5000);
        
        // Update port display every second
        setInterval(function() {{
            document.getElementById('currentPort').textContent = window.location.port || '80';
        }}, 1000);
    </script>
</body>
</html>
            """
            
            self.wfile.write(html.encode())
            return
        else:
            self.send_response(404)
            self.end_headers()

def start_server():
    global PORT
    try:
        with socketserver.TCPServer(("", PORT), WebHandler) as httpd:
            httpd.allow_reuse_address = True
            print(f"🌐 Web server running on port {PORT}")
            print(f"🔗 Access: http://localhost:{PORT}")
            
            # Auto-open browser
            webbrowser.open(f'http://localhost:{PORT}')
            
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ Server error: {e}")

def create_gui():
    root = tk.Tk()
    root.title("🌐 Web Control System")
    root.geometry("450x300")
    root.configure(bg='#000000')
    
    # Make window appear
    root.lift()
    root.attributes('-topmost', True)
    root.after_idle(root.attributes, '-topmost', False)
    
    title = tk.Label(root, text="🌐 WEB CONTROL SYSTEM", font=("Arial", 18, "bold"), fg="#4a9eff", bg="#000000")
    title.pack(pady=20)
    
    port_label = tk.Label(root, text=f"🔗 Running on Port: {PORT}", font=("Arial", 14, "bold"), fg="#00ff88", bg="#000000")
    port_label.pack(pady=10)
    
    status = tk.Label(root, text="✅ Web interface active in browser", font=("Arial", 12), fg="#7bb3ff", bg="#000000")
    status.pack(pady=10)
    
    def new_port():
        global PORT
        PORT = find_free_port()
        port_label.config(text=f"🔗 New Port: {PORT}")
        status.config(text="Port changed - restart server to apply")
    
    new_port_btn = tk.Button(root, text="🔄 NEW PORT", command=new_port, font=("Arial", 11, "bold"), 
                            bg="#ff6600", fg="#000000", padx=20, pady=8)
    new_port_btn.pack(pady=15)
    
    open_btn = tk.Button(root, text="🌐 OPEN BROWSER", 
                        command=lambda: webbrowser.open(f'http://localhost:{PORT}'),
                        font=("Arial", 12, "bold"), bg="#4a9eff", fg="#000000", padx=20, pady=10)
    open_btn.pack(pady=10)
    
    # Auto-start server
    Thread(target=start_server, daemon=True).start()
    
    # Auto port rotation every 30 seconds
    def auto_rotate():
        new_port()
        root.after(30000, auto_rotate)
    
    root.after(30000, auto_rotate)
    
    root.mainloop()

if __name__ == "__main__":
    create_gui()
