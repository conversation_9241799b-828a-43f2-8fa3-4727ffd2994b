#!/usr/bin/env python3
"""
Web-Only Launcher - Optimized for online interface
"""

import http.server
import socketserver
import subprocess
import tkinter as tk
from threading import Thread
import time
import random
import webbrowser

def find_free_port():
    return random.randint(8000, 9999)

PORT = find_free_port()

class WebHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blackhat Control System</title>
    <style>
        body {{
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: radial-gradient(circle at center, #0a0a0f 0%, #000000 100%);
            color: #4a9eff;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }}
        .header {{
            background: linear-gradient(135deg, #001122 0%, #002244 50%, #001122 100%);
            padding: 25px 0;
            text-align: center;
            box-shadow: 0 8px 30px rgba(74, 158, 255, 0.3);
            border-bottom: 2px solid #4a9eff;
            margin-bottom: 30px;
            border-radius: 12px;
            animation: pulse 3s infinite;
        }}
        @keyframes pulse {{
            0%, 100% {{ box-shadow: 0 8px 30px rgba(74, 158, 255, 0.3); }}
            50% {{ box-shadow: 0 8px 30px rgba(74, 158, 255, 0.6); }}
        }}
        .header h1 {{
            font-size: 3rem;
            font-weight: 900;
            color: #4a9eff;
            text-shadow: 0 0 20px #4a9eff, 0 0 40px #4a9eff;
            margin-bottom: 10px;
            letter-spacing: 3px;
            text-transform: uppercase;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }}
        .card {{
            background: linear-gradient(135deg, #0d0d0d 0%, #001122 50%, #0d0d0d 100%);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(74, 158, 255, 0.2);
            border: 1px solid #4a9eff;
            transition: all 0.3s ease;
        }}
        .card:hover {{
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(74, 158, 255, 0.4);
        }}
        .card-title {{
            font-size: 1.3rem;
            font-weight: 700;
            color: #4a9eff;
            margin-bottom: 20px;
            text-transform: uppercase;
        }}
        .form-input {{
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #4a9eff;
            border-radius: 8px;
            background: #0a0a0a;
            color: #7bb3ff;
            font-size: 1rem;
            margin: 10px 0;
            box-sizing: border-box;
        }}
        .btn {{
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            background: linear-gradient(135deg, #4a9eff 0%, #2d7dd2 100%);
            color: #ffffff;
            margin: 5px;
            transition: all 0.3s ease;
        }}
        .btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 158, 255, 0.5);
        }}
        .console {{
            background: #000000;
            border: 2px solid #4a9eff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            min-height: 150px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
            color: #4a9eff;
        }}
        .port-info {{
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #4a9eff;
            border-radius: 8px;
            padding: 10px;
            font-family: monospace;
            color: #00ff88;
        }}
        .status-indicator {{
            background: linear-gradient(135deg, #003366 0%, #001a33 100%);
            border: 2px solid #4a9eff;
            color: #7bb3ff;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="port-info">
        🌐 Port: <span id="currentPort">{PORT}</span>
    </div>
    
    <div class="header">
        <h1>⚠️ BLACKHAT CONTROL SYSTEM ⚠️</h1>
        <p style="color: #7bb3ff; font-size: 1.2rem;">⚡ WEB INTERFACE • DYNAMIC PORT ROTATION • MAXIMUM STEALTH ⚡</p>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-title">💀 STEALTH MESSAGING</div>
            
            <div class="status-indicator" id="smsStatus">
                🔒 STEALTH MODE ACTIVE - UNTRACEABLE OPERATIONS
            </div>
            
            <input type="text" class="form-input" id="phoneNumber" placeholder="Target Phone Number: +1234567890">
            <textarea class="form-input" id="messageText" rows="3" placeholder="Message (Leave empty for silent Play Store popup)"></textarea>
            
            <button class="btn" onclick="sendSMS()">📱 SEND ANONYMOUS SMS</button>
            
            <div id="smsOutput" class="console">
                <div style="color: #4a9eff;">💀 STEALTH MESSAGING SYSTEM ACTIVE...</div>
                <div style="color: #7bb3ff;">⚠️ READY FOR ANONYMOUS OPERATIONS ⚠️</div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">👁️ REMOTE SURVEILLANCE</div>
            
            <input type="text" class="form-input" id="deviceId" placeholder="Device ID: target_device_001">
            <button class="btn" onclick="takeScreenshot()">📸 CAPTURE SCREEN</button>
            <button class="btn" onclick="freezeScreen()">❄️ FREEZE SCREEN</button>
            
            <div id="hvncOutput" class="console">
                <div style="color: #4a9eff;">👁️ SURVEILLANCE SYSTEM ACTIVE...</div>
                <div style="color: #7bb3ff;">⚠️ MONITORING ALL TARGETS ⚠️</div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">🏴‍☠️ DATA EXTRACTION</div>
            
            <button class="btn" onclick="harvestCrypto()">₿ HARVEST CRYPTO</button>
            <button class="btn" onclick="harvestBanking()">🏦 HARVEST BANKING</button>
            <button class="btn" onclick="extractCookies()">🍪 EXTRACT COOKIES</button>
            
            <div id="harvestOutput" class="console">
                <div style="color: #4a9eff;">🏴‍☠️ DATA EXTRACTION PROTOCOLS LOADED...</div>
                <div style="color: #7bb3ff;">⚠️ HARVESTING BANKING, CRYPTO & PERSONAL DATA ⚠️</div>
            </div>
        </div>
    </div>

    <script>
        function getTimestamp() {{
            return new Date().toLocaleTimeString();
        }}
        
        function getRandomElement(array) {{
            return array[Math.floor(Math.random() * array.length)];
        }}
        
        function sendSMS() {{
            const phone = document.getElementById('phoneNumber').value;
            const message = document.getElementById('messageText').value;
            const output = document.getElementById('smsOutput');
            const timestamp = getTimestamp();
            const devices = ['Android_001', 'iPhone_007', 'Samsung_Galaxy', 'Pixel_Device'];
            const locations = ['New York', 'London', 'Berlin', 'Tokyo', 'Sydney', 'Toronto'];
            
            if (!phone) {{
                output.innerHTML += '<div style="color: #ff6666;">❌ Phone number required</div>';
                return;
            }}
            
            if (!message.trim()) {{
                output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 📱 Silent deployment to ' + phone + '</div>';
                output.innerHTML += '<div style="color: #7bb3ff;">🎯 Fake Play Store popup triggered via ' + getRandomElement(devices) + '</div>';
                output.innerHTML += '<div style="color: #ffaa00;">⚠️ Location spoofed: ' + getRandomElement(locations) + ' - No SMS trace</div>';
                output.innerHTML += '<div style="color: #66ff66;">✅ APK payload delivered successfully</div>';
            }} else {{
                output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 📱 Anonymous SMS sent to ' + phone + '</div>';
                output.innerHTML += '<div style="color: #7bb3ff;">💬 Message: ' + message + '</div>';
                output.innerHTML += '<div style="color: #ffaa00;">🔥 Auto-delete in ' + Math.floor(Math.random() * 15 + 5) + ' seconds</div>';
                output.innerHTML += '<div style="color: #66ff66;">📍 Routed through: ' + getRandomElement(locations) + '</div>';
            }}
            
            output.scrollTop = output.scrollHeight;
        }}
        
        function takeScreenshot() {{
            const deviceId = document.getElementById('deviceId').value || getRandomElement(['Android_001', 'iPhone_007', 'Samsung_Galaxy']);
            const output = document.getElementById('hvncOutput');
            const timestamp = getTimestamp();
            const locations = ['New York', 'London', 'Berlin', 'Tokyo', 'Sydney'];
            const resolution = Math.floor(Math.random() * 500 + 1080) + 'x' + Math.floor(Math.random() * 300 + 1920);
            
            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 📸 Screenshot captured from ' + deviceId + '</div>';
            output.innerHTML += '<div style="color: #7bb3ff;">👁️ Resolution: ' + resolution + ' | Location: ' + getRandomElement(locations) + '</div>';
            output.innerHTML += '<div style="color: #ffaa00;">🔍 Screen monitoring active - ' + Math.floor(Math.random() * 50 + 10) + ' apps detected</div>';
            output.innerHTML += '<div style="color: #66ff66;">💾 Image saved: screenshot_' + Date.now() + '.png</div>';
            
            output.scrollTop = output.scrollHeight;
        }}
        
        function freezeScreen() {{
            const deviceId = document.getElementById('deviceId').value || 'unknown_device';
            const output = document.getElementById('hvncOutput');
            const timestamp = getTimestamp();
            
            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] ❄️ Screen freeze activated on ' + deviceId + '</div>';
            output.innerHTML += '<div style="color: #ff6666;">🚫 User input blocked for ' + Math.floor(Math.random() * 90 + 30) + ' seconds</div>';
            output.innerHTML += '<div style="color: #ffaa00;">⚡ Takeover mode enabled</div>';
            
            output.scrollTop = output.scrollHeight;
        }}
        
        function harvestCrypto() {{
            const output = document.getElementById('harvestOutput');
            const timestamp = getTimestamp();
            const platforms = ['Binance', 'Coinbase', 'Kraken', 'MetaMask', 'Trust Wallet', 'Exodus'];
            const cryptos = ['BTC', 'ETH', 'BNB', 'USDT', 'ADA', 'DOT'];
            
            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] ₿ Crypto harvesting initiated...</div>';
            
            for (let i = 0; i < 3; i++) {{
                const platform = getRandomElement(platforms);
                const crypto = getRandomElement(cryptos);
                const amount = (Math.random() * 10 + 0.1).toFixed(2);
                const usdValue = Math.floor(Math.random() * 50000 + 10000);
                output.innerHTML += '<div style="color: #7bb3ff;">💰 ' + platform + ': ' + amount + ' ' + crypto + ' ($' + usdValue.toLocaleString() + ')</div>';
            }}
            
            const totalValue = Math.floor(Math.random() * 150000 + 50000);
            output.innerHTML += '<div style="color: #66ff66;">💎 Total extracted: $' + totalValue.toLocaleString() + '</div>';
            
            output.scrollTop = output.scrollHeight;
        }}
        
        function harvestBanking() {{
            const output = document.getElementById('harvestOutput');
            const timestamp = getTimestamp();
            const banks = [
                {{name: 'Chase Bank', country: 'USA', symbol: '$'}},
                {{name: 'HSBC', country: 'UK', symbol: '£'}},
                {{name: 'Deutsche Bank', country: 'Germany', symbol: '€'}},
                {{name: 'BNP Paribas', country: 'France', symbol: '€'}},
                {{name: 'Santander', country: 'Spain', symbol: '€'}}
            ];
            
            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 🏦 Banking data extraction...</div>';
            
            for (let i = 0; i < 3; i++) {{
                const bank = getRandomElement(banks);
                const amount = Math.floor(Math.random() * 450000 + 50000);
                output.innerHTML += '<div style="color: #7bb3ff;">💳 ' + bank.name + ' (' + bank.country + '): ' + bank.symbol + amount.toLocaleString() + '</div>';
            }}
            
            const accounts = Math.floor(Math.random() * 20 + 5);
            output.innerHTML += '<div style="color: #66ff66;">🔐 ' + accounts + ' accounts compromised</div>';
            
            output.scrollTop = output.scrollHeight;
        }}
        
        function extractCookies() {{
            const output = document.getElementById('harvestOutput');
            const timestamp = getTimestamp();
            const sites = ['amazon.com', 'paypal.com', 'facebook.com', 'google.com', 'microsoft.com', 'apple.com', 'netflix.com', 'spotify.com'];
            
            output.innerHTML += '<div style="color: #00ff88;">[' + timestamp + '] 🍪 Cookie extraction initiated...</div>';
            
            for (let i = 0; i < 4; i++) {{
                const site = getRandomElement(sites);
                const cookies = Math.floor(Math.random() * 135 + 15);
                output.innerHTML += '<div style="color: #7bb3ff;">🔓 ' + site + ': ' + cookies + ' cookies extracted</div>';
            }}
            
            output.innerHTML += '<div style="color: #66ff66;">📊 Session tokens harvested</div>';
            
            output.scrollTop = output.scrollHeight;
        }}
        
        // Auto-refresh status indicators every 5 seconds
        setInterval(function() {{
            const indicators = document.querySelectorAll('.status-indicator');
            const messages = [
                '🔒 STEALTH MODE ACTIVE - UNTRACEABLE OPERATIONS',
                '⚡ MAXIMUM ANONYMITY - ALL SYSTEMS OPERATIONAL',
                '🛡️ ENCRYPTED CHANNELS - ZERO DETECTION RISK',
                '🌐 TOR ROUTING ACTIVE - COMPLETE INVISIBILITY'
            ];
            indicators.forEach(function(indicator) {{
                indicator.textContent = getRandomElement(messages);
            }});
        }}, 5000);
        
        // Update port display every second
        setInterval(function() {{
            document.getElementById('currentPort').textContent = window.location.port || '80';
        }}, 1000);
    </script>
</body>
</html>
            """
            
            self.wfile.write(html.encode())
            return
        else:
            self.send_response(404)
            self.end_headers()

def start_server():
    global PORT
    try:
        with socketserver.TCPServer(("", PORT), WebHandler) as httpd:
            httpd.allow_reuse_address = True
            print(f"🌐 Web server running on port {PORT}")
            print(f"🔗 Access: http://localhost:{PORT}")
            
            # Auto-open browser
            webbrowser.open(f'http://localhost:{PORT}')
            
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ Server error: {e}")

def create_gui():
    root = tk.Tk()
    root.title("🌐 Web Control System")
    root.geometry("450x300")
    root.configure(bg='#000000')
    
    # Make window appear
    root.lift()
    root.attributes('-topmost', True)
    root.after_idle(root.attributes, '-topmost', False)
    
    title = tk.Label(root, text="🌐 WEB CONTROL SYSTEM", font=("Arial", 18, "bold"), fg="#4a9eff", bg="#000000")
    title.pack(pady=20)
    
    port_label = tk.Label(root, text=f"🔗 Running on Port: {PORT}", font=("Arial", 14, "bold"), fg="#00ff88", bg="#000000")
    port_label.pack(pady=10)
    
    status = tk.Label(root, text="✅ Web interface active in browser", font=("Arial", 12), fg="#7bb3ff", bg="#000000")
    status.pack(pady=10)
    
    def new_port():
        global PORT
        PORT = find_free_port()
        port_label.config(text=f"🔗 New Port: {PORT}")
        status.config(text="Port changed - restart server to apply")
    
    new_port_btn = tk.Button(root, text="🔄 NEW PORT", command=new_port, font=("Arial", 11, "bold"), 
                            bg="#ff6600", fg="#000000", padx=20, pady=8)
    new_port_btn.pack(pady=15)
    
    open_btn = tk.Button(root, text="🌐 OPEN BROWSER", 
                        command=lambda: webbrowser.open(f'http://localhost:{PORT}'),
                        font=("Arial", 12, "bold"), bg="#4a9eff", fg="#000000", padx=20, pady=10)
    open_btn.pack(pady=10)
    
    # Auto-start server
    Thread(target=start_server, daemon=True).start()
    
    # Auto port rotation every 30 seconds
    def auto_rotate():
        new_port()
        root.after(30000, auto_rotate)
    
    root.after(30000, auto_rotate)
    
    root.mainloop()

if __name__ == "__main__":
    create_gui()
